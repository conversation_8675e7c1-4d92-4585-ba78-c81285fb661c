import { Trade } from '../types/trade';

// Dhan API Types
export interface DhanProfile {
  dhanClientId: string;
  tokenValidity: string;
  activeSegment: string;
  ddpi: string;
  mtf: string;
  dataPlan: string;
  dataValidity: string;
}

export interface DhanOrder {
  dhanClientId: string;
  orderId: string;
  correlationId: string;
  orderStatus: 'TRANSIT' | 'PENDING' | 'REJECTED' | 'CANCELLED' | 'PART_TRADED' | 'TRADED' | 'EXPIRED';
  transactionType: 'BUY' | 'SELL';
  exchangeSegment: string;
  productType: 'CNC' | 'INTRADAY' | 'MARGIN' | 'MTF' | 'CO' | 'BO';
  orderType: 'LIMIT' | 'MARKET' | 'STOP_LOSS' | 'STOP_LOSS_MARKET';
  validity: 'DAY' | 'IOC';
  tradingSymbol: string;
  securityId: string;
  quantity: number;
  disclosedQuantity: number;
  price: number;
  triggerPrice: number;
  afterMarketOrder: boolean;
  boProfitValue: number;
  boStopLossValue: number;
  legName: 'ENTRY_LEG' | 'TARGET_LEG' | 'STOP_LOSS_LEG' | null;
  createTime: string;
  updateTime: string;
  exchangeTime: string;
  drvExpiryDate: number | null;
  drvOptionType: 'CALL' | 'PUT' | null;
  drvStrikePrice: number;
  omsErrorCode: string | null;
  omsErrorDescription: string | null;
  algoId: string;
  remainingQuantity: number;
  averageTradedPrice: number;
  filledQty: number;
}

export interface DhanTrade {
  dhanClientId: string;
  orderId: string;
  exchangeOrderId: string;
  exchangeTradeId: string;
  transactionType: 'BUY' | 'SELL';
  exchangeSegment: string;
  productType: 'CNC' | 'INTRADAY' | 'MARGIN' | 'MTF' | 'CO' | 'BO';
  orderType: 'LIMIT' | 'MARKET' | 'STOP_LOSS' | 'STOP_LOSS_MARKET';
  tradingSymbol: string;
  securityId: string;
  tradedQuantity: number;
  tradedPrice: number;
  createTime: string;
  updateTime: string;
  exchangeTime: string;
  drvExpiryDate: number | null;
  drvOptionType: 'CALL' | 'PUT' | null;
  drvStrikePrice: number;
}

export interface DhanForeverOrder {
  dhanClientId: string;
  orderId: string;
  orderStatus: 'TRANSIT' | 'PENDING' | 'REJECTED' | 'CANCELLED' | 'TRADED' | 'EXPIRED' | 'CONFIRM';
  transactionType: 'BUY' | 'SELL';
  exchangeSegment: 'NSE_EQ' | 'NSE_FNO' | 'BSE_EQ' | 'MCX_COMM';
  productType: 'CNC' | 'INTRADAY' | 'MARGIN' | 'CO' | 'BO';
  orderType: 'SINGLE' | 'OCO';
  tradingSymbol: string;
  securityId: string;
  quantity: number;
  price: number;
  triggerPrice: number;
  legName: 'ENTRY_LEG' | 'TARGET_LEG';
  createTime: string;
  updateTime: string | null;
  exchangeTime: string | null;
  drvExpiryDate: string | null;
  drvOptionType: 'CALL' | 'PUT' | null;
  drvStrikePrice: number;
}

export interface DhanHolding {
  exchange: string;
  tradingSymbol: string;
  securityId: string;
  isin: string;
  totalQty: number;
  dpQty: number;
  t1Qty: number;
  availableQty: number;
  collateralQty: number;
  avgCostPrice: number;
}

export interface DhanPosition {
  dhanClientId: string;
  tradingSymbol: string;
  securityId: string;
  positionType: 'LONG' | 'SHORT' | 'CLOSED';
  exchangeSegment: 'NSE_EQ' | 'NSE_FNO' | 'NSE_CURRENCY' | 'BSE_EQ' | 'BSE_FNO' | 'BSE_CURRENCY' | 'MCX_COMM';
  productType: 'CNC' | 'INTRADAY' | 'MARGIN' | 'MTF' | 'CO' | 'BO';
  buyAvg: number;
  buyQty: number;
  costPrice: number;
  sellAvg: number;
  sellQty: number;
  netQty: number;
  realizedProfit: number;
  unrealizedProfit: number;
  rbiReferenceRate: number;
  multiplier: number;
  carryForwardBuyQty: number;
  carryForwardSellQty: number;
  carryForwardBuyValue: number;
  carryForwardSellValue: number;
  dayBuyQty: number;
  daySellQty: number;
  dayBuyValue: number;
  daySellValue: number;
  drvExpiryDate: string;
  drvOptionType: 'CALL' | 'PUT' | null;
  drvStrikePrice: number;
  crossCurrency: boolean;
}

export interface DhanLedgerEntry {
  dhanClientId: string;
  narration: string;
  voucherdate: string;
  exchange: string;
  voucherdesc: string;
  vouchernumber: string;
  debit: string;
  credit: string;
  runbal: string;
}

export interface DhanTradeHistory {
  dhanClientId: string;
  orderId: string;
  exchangeOrderId: string;
  exchangeTradeId: string;
  transactionType: 'BUY' | 'SELL';
  exchangeSegment: string;
  productType: 'CNC' | 'INTRADAY' | 'MARGIN' | 'MTF' | 'CO' | 'BO';
  orderType: 'LIMIT' | 'MARKET' | 'STOP_LOSS' | 'STOP_LOSS_MARKET';
  tradingSymbol: string | null;
  customSymbol: string;
  securityId: string;
  tradedQuantity: number;
  tradedPrice: number;
  isin: string;
  instrument: 'EQUITY' | 'DERIVATIVES';
  sebiTax: number;
  stt: number;
  brokerageCharges: number;
  serviceTax: number;
  exchangeTransactionCharges: number;
  stampDuty: number;
  createTime: string;
  updateTime: string;
  exchangeTime: string;
  drvExpiryDate: string;
  drvOptionType: 'CALL' | 'PUT' | string;
  drvStrikePrice: number;
}

export interface DhanApiError {
  errorType: string;
  errorCode: string;
  errorMessage: string;
}

// Fund Management Types
export interface DhanMarginCalculatorRequest {
  dhanClientId: string;
  exchangeSegment: 'NSE_EQ' | 'NSE_FNO' | 'BSE_EQ' | 'BSE_FNO' | 'MCX_COMM';
  transactionType: 'BUY' | 'SELL';
  quantity: number;
  productType: 'CNC' | 'INTRADAY' | 'MARGIN' | 'MTF' | 'CO' | 'BO';
  securityId: string;
  price: number;
  triggerPrice?: number;
}

export interface DhanMarginCalculatorResponse {
  totalMargin: number;
  spanMargin: number;
  exposureMargin: number;
  availableBalance: number;
  variableMargin: number;
  insufficientBalance: number;
  brokerage: number;
  leverage: string;
}

export interface DhanFundLimit {
  dhanClientId: string;
  availabelBalance: number; // Note: API has typo "availabel"
  sodLimit: number;
  collateralAmount: number;
  receiveableAmount: number;
  utilizedAmount: number;
  blockedPayoutAmount: number;
  withdrawableBalance: number;
}

export class DhanApiService {
  private static readonly BASE_URL = (() => {
    // For development, always use the proxy server
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
      return 'http://localhost:3001/api/dhan'; // Back to port 3001
    }
    // For production, use relative path (assuming proxy is on same domain)
    return '/api/dhan';
  })();
  private static accessToken: string | null = null;

  /**
   * Set the access token for API calls
   */
  static setAccessToken(token: string): void {
    this.accessToken = token;
    // Store in localStorage for persistence
    localStorage.setItem('dhan_access_token', token);
  }

  /**
   * Get the stored access token
   */
  static getAccessToken(): string | null {
    if (!this.accessToken) {
      this.accessToken = localStorage.getItem('dhan_access_token');
    }
    return this.accessToken;
  }

  /**
   * Clear the access token
   */
  static clearAccessToken(): void {
    this.accessToken = null;
    localStorage.removeItem('dhan_access_token');
  }

  /**
   * Make API request with proper headers
   */
  private static async makeRequest<T>(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    body?: any
  ): Promise<T> {
    const token = this.getAccessToken();
    if (!token) {
      throw new Error('Dhan access token not set. Please configure your API token.');
    }

    const url = `${this.BASE_URL}${endpoint}`;

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'access-token': token,
    };

    const config: RequestInit = {
      method,
      headers,
      mode: 'cors',
      credentials: 'omit'
    };

    if (body && (method === 'POST' || method === 'PUT')) {
      config.body = JSON.stringify(body);
    }

    try {
      // Validate URL before making request
      new URL(url); // This will throw if URL is invalid

      const response = await fetch(url, config);

      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        let errorCode = '';

        try {
          const errorData: DhanApiError = await response.json();
          errorMessage = errorData.errorMessage || errorMessage;
          errorCode = errorData.errorCode || '';

          // Add specific error handling for common issues
          if (errorCode === 'DH-907' || errorMessage.includes('Invalid Request')) {
            errorMessage += ' (Possible causes: Invalid date range, future dates, or no data available for the selected period)';
          }
        } catch (e) {
          // If we can't parse error as JSON, use the status text
          if (response.status === 500) {
            errorMessage += ' (Server error - check date range and API parameters)';
          }
        }

        console.error('🔍 Dhan API Error Details:', { status: response.status, errorCode, errorMessage, url });
        throw new Error(`Dhan API Error: ${errorMessage}`);
      }

      const data = await response.json();
      console.log(`✅ Request successful`);
      return data;
    } catch (error) {
      console.error('❌ Dhan API request failed:', error);
      console.error('Request details:', { url, method, headers: { ...headers, 'access-token': '[REDACTED]' } });
      throw error;
    }
  }

  /**
   * Get user profile to verify token validity
   */
  static async getProfile(): Promise<DhanProfile> {
    return this.makeRequest<DhanProfile>('/profile');
  }

  /**
   * Get all orders for the day
   */
  static async getOrders(): Promise<DhanOrder[]> {
    return this.makeRequest<DhanOrder[]>('/orders');
  }

  /**
   * Get order by order ID
   */
  static async getOrderById(orderId: string): Promise<DhanOrder> {
    return this.makeRequest<DhanOrder>(`/orders/${orderId}`);
  }

  /**
   * Get order by correlation ID
   */
  static async getOrderByCorrelationId(correlationId: string): Promise<DhanOrder> {
    return this.makeRequest<DhanOrder>(`/orders/external/${correlationId}`);
  }

  /**
   * Get all trades for the day
   */
  static async getTrades(): Promise<DhanTrade[]> {
    return this.makeRequest<DhanTrade[]>('/trades');
  }

  /**
   * Get trades for a specific order
   */
  static async getTradesByOrderId(orderId: string): Promise<DhanTrade[]> {
    return this.makeRequest<DhanTrade[]>(`/trades/${orderId}`);
  }

  /**
   * Get all forever orders
   */
  static async getForeverOrders(): Promise<DhanForeverOrder[]> {
    return this.makeRequest<DhanForeverOrder[]>('/forever/all');
  }

  /**
   * Get all holdings
   */
  static async getHoldings(): Promise<DhanHolding[]> {
    return this.makeRequest<DhanHolding[]>('/holdings');
  }

  /**
   * Get all positions
   */
  static async getPositions(): Promise<DhanPosition[]> {
    return this.makeRequest<DhanPosition[]>('/positions');
  }

  /**
   * Get ledger report for a date range
   */
  static async getLedger(fromDate: string, toDate: string): Promise<DhanLedgerEntry[]> {
    console.log(`📊 Fetching ledger from ${fromDate} to ${toDate}`);
    return this.makeRequest<DhanLedgerEntry[]>(`/ledger?from-date=${fromDate}&to-date=${toDate}`);
  }

  /**
   * Get trade history for a date range with pagination
   */
  static async getTradeHistory(fromDate: string, toDate: string, page: number = 0): Promise<DhanTradeHistory[]> {
    return this.makeRequest<DhanTradeHistory[]>(`/trades/${fromDate}/${toDate}/${page}`);
  }

  /**
   * Get current configuration for debugging
   */
  static getDebugInfo(): { baseUrl: string; hasToken: boolean; hostname: string } {
    return {
      baseUrl: this.BASE_URL,
      hasToken: !!this.getAccessToken(),
      hostname: window.location.hostname
    };
  }

  /**
   * Test API connection
   */
  static async testConnection(): Promise<{ success: boolean; message: string; profile?: DhanProfile }> {
    try {
      console.log('🔍 Debug info:', this.getDebugInfo());
      const profile = await this.getProfile();
      return {
        success: true,
        message: 'Successfully connected to Dhan API',
        profile
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Test ledger API with a safe date range
   */
  static async testLedgerAPI(): Promise<{ success: boolean; message: string; sampleData?: any }> {
    try {
      // Use a very recent date range that's likely to work
      const today = new Date();
      const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

      const fromDate = lastWeek.toISOString().split('T')[0];
      const toDate = today.toISOString().split('T')[0];

      console.log('🧪 Testing ledger API with dates:', { fromDate, toDate });

      const data = await this.getLedger(fromDate, toDate);
      return {
        success: true,
        message: `Ledger API test successful. Found ${Array.isArray(data) ? data.length : 1} entries.`,
        sampleData: data
      };
    } catch (error) {
      return {
        success: false,
        message: `Ledger API test failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Convert Dhan trades to Trade Journal format
   */
  static convertDhanTradesToJournalTrades(
    dhanTrades: DhanTrade[],
    portfolioSize: number
  ): Partial<Trade>[] {
    // Group trades by trading symbol and date
    const tradeGroups = new Map<string, DhanTrade[]>();
    
    dhanTrades.forEach(trade => {
      const key = `${trade.tradingSymbol}_${trade.createTime.split(' ')[0]}`;
      if (!tradeGroups.has(key)) {
        tradeGroups.set(key, []);
      }
      tradeGroups.get(key)!.push(trade);
    });

    const journalTrades: Partial<Trade>[] = [];

    tradeGroups.forEach((trades, key) => {
      const [symbol, date] = key.split('_');
      
      // Separate buy and sell trades
      const buyTrades = trades.filter(t => t.transactionType === 'BUY');
      const sellTrades = trades.filter(t => t.transactionType === 'SELL');

      if (buyTrades.length > 0) {
        // Calculate average entry price and total quantity
        const totalBuyQty = buyTrades.reduce((sum, t) => sum + t.tradedQuantity, 0);
        const totalBuyValue = buyTrades.reduce((sum, t) => sum + (t.tradedQuantity * t.tradedPrice), 0);
        const avgEntry = totalBuyValue / totalBuyQty;

        // Calculate position size
        const positionSize = totalBuyValue;
        const allocation = portfolioSize > 0 ? (positionSize / portfolioSize) * 100 : 0;

        const journalTrade: Partial<Trade> = {
          name: symbol,
          date: date,
          buySell: 'Buy',
          entry: buyTrades[0].tradedPrice,
          avgEntry: avgEntry,
          initialQty: totalBuyQty,
          positionSize: positionSize,
          allocation: allocation,
          positionStatus: sellTrades.length > 0 ? 'Closed' : 'Open',
          // Add Dhan-specific metadata
          _dhanMetadata: {
            orderIds: buyTrades.map(t => t.orderId),
            exchangeOrderIds: buyTrades.map(t => t.exchangeOrderId),
            productType: buyTrades[0].productType,
            exchangeSegment: buyTrades[0].exchangeSegment
          }
        };

        // If there are sell trades, calculate exit details
        if (sellTrades.length > 0) {
          const totalSellQty = sellTrades.reduce((sum, t) => sum + t.tradedQuantity, 0);
          const totalSellValue = sellTrades.reduce((sum, t) => sum + (t.tradedQuantity * t.tradedPrice), 0);
          const avgExit = totalSellValue / totalSellQty;

          journalTrade.exit1Price = avgExit;
          journalTrade.exit1Qty = totalSellQty;
          journalTrade.exit1Date = sellTrades[0].createTime.split(' ')[0];
          journalTrade.avgExitPrice = avgExit;
          journalTrade.exitedQty = totalSellQty;
          journalTrade.openQty = totalBuyQty - totalSellQty;
          
          if (journalTrade.openQty === 0) {
            journalTrade.positionStatus = 'Closed';
          } else {
            journalTrade.positionStatus = 'Partial';
          }

          // Calculate P&L
          const realizedPL = (avgExit - avgEntry) * totalSellQty;
          journalTrade.realisedAmount = realizedPL;
          journalTrade.plRs = realizedPL;
          journalTrade.pfImpact = portfolioSize > 0 ? (realizedPL / portfolioSize) * 100 : 0;
        }

        journalTrades.push(journalTrade);
      }
    });

    return journalTrades;
  }

  /**
   * Calculate margin requirements for an order
   */
  static async calculateMargin(request: DhanMarginCalculatorRequest): Promise<DhanMarginCalculatorResponse> {
    return this.makeRequest<DhanMarginCalculatorResponse>('/margincalculator', {
      method: 'POST',
      body: JSON.stringify(request)
    });
  }

  /**
   * Get trading account fund information
   */
  static async getFundLimit(): Promise<DhanFundLimit> {
    return this.makeRequest<DhanFundLimit>('/fundlimit');
  }

  /**
   * Fetch instrument list and create securityId to symbol mapping
   * For now, returns empty map due to CORS issues - will use fallback symbol creation
   */
  static async getInstrumentSymbolMap(): Promise<Map<string, string>> {
    try {
      console.log('🔍 Attempting to fetch instrument list for symbol mapping...');
      console.log('⚠️ Note: Instrument list fetching is currently disabled due to CORS restrictions');
      console.log('📋 Using fallback symbol creation from customSymbol instead');

      // TODO: Implement proper proxy for instrument list CSV
      // For now, return empty map to use fallback symbol creation
      return new Map();

    } catch (error) {
      console.error('❌ Failed to fetch instrument list:', error);
      return new Map(); // Return empty map on error
    }
  }



  /**
   * Get symbol name from security ID using instrument list
   */
  static async getSymbolFromSecurityId(securityId: string): Promise<string | null> {
    try {
      const symbolMap = await this.getInstrumentSymbolMap();
      return symbolMap.get(securityId) || null;
    } catch (error) {
      console.error('❌ Failed to get symbol from security ID:', error);
      return null;
    }
  }
}
