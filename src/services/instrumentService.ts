// Dhan Instrument Service for fetching and searching instruments
export interface DhanInstrument {
  SEM_EXM_EXCH_ID: string; // Exchange (NSE, BSE, MCX)
  SEM_SEGMENT: string; // Segment (E=Equity, D=Derivatives, etc.)
  SEM_INSTRUMENT_NAME: string; // Instrument type
  SM_SYMBOL_NAME: string; // Symbol name
  SEM_CUSTOM_SYMBOL: string; // Display name
  SEM_EXCH_INSTRUMENT_TYPE: string; // Exchange instrument type
  SEM_SERIES: string; // Series
  SEM_LOT_UNITS: string; // Lot size
  SEM_EXPIRY_DATE?: string; // Expiry date for derivatives
  SEM_STRIKE_PRICE?: string; // Strike price for options
  SEM_OPTION_TYPE?: string; // CE/PE for options
  SEM_TICK_SIZE: string; // Tick size
  SEM_EXPIRY_FLAG?: string; // M/W for monthly/weekly expiry
  // Security ID is the index/row number in the CSV
}

export interface ParsedInstrument {
  securityId: string;
  symbol: string;
  displayName: string;
  exchange: string;
  segment: string;
  instrumentType: string;
  series: string;
  lotSize: number;
  tickSize: number;
  expiryDate?: string;
  strikePrice?: number;
  optionType?: 'CE' | 'PE';
  expiryFlag?: 'M' | 'W';
}

class InstrumentService {
  private static instruments: ParsedInstrument[] = [];
  private static lastFetchTime: number = 0;
  private static readonly CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours
  private static readonly COMPACT_CSV_URL = 'https://images.dhan.co/api-data/api-scrip-master.csv';

  /**
   * Fetch instruments from Dhan API
   */
  static async fetchInstruments(forceRefresh: boolean = false): Promise<ParsedInstrument[]> {
    const now = Date.now();
    
    // Return cached data if available and not expired
    if (!forceRefresh && this.instruments.length > 0 && (now - this.lastFetchTime) < this.CACHE_DURATION) {
      return this.instruments;
    }

    try {
      const response = await fetch(this.COMPACT_CSV_URL);
      if (!response.ok) {
        throw new Error(`Failed to fetch instruments: ${response.status}`);
      }

      const csvText = await response.text();
      this.instruments = this.parseCSV(csvText);
      this.lastFetchTime = now;

      // Cache in localStorage for persistence
      try {
        localStorage.setItem('dhan_instruments', JSON.stringify({
          data: this.instruments,
          timestamp: now
        }));
      } catch (e) {
        // Ignore localStorage errors
      }

      return this.instruments;
    } catch (error) {
      // Try to load from localStorage cache as fallback
      try {
        const cached = localStorage.getItem('dhan_instruments');
        if (cached) {
          const { data, timestamp } = JSON.parse(cached);
          if (data && Array.isArray(data) && (now - timestamp) < (7 * 24 * 60 * 60 * 1000)) { // 7 days fallback
            this.instruments = data;
            this.lastFetchTime = timestamp;
            return this.instruments;
          }
        }
      } catch (e) {
        // Ignore cache errors
      }

      throw new Error(`Failed to fetch instruments: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Parse CSV data into structured instruments
   */
  private static parseCSV(csvText: string): ParsedInstrument[] {
    const lines = csvText.trim().split('\n');
    const headers = lines[0].split(',');
    
    // Find column indices
    const getColumnIndex = (columnName: string) => {
      const index = headers.findIndex(h => h.trim() === columnName);
      return index >= 0 ? index : -1;
    };

    const indices = {
      exchange: getColumnIndex('SEM_EXM_EXCH_ID'),
      segment: getColumnIndex('SEM_SEGMENT'),
      instrument: getColumnIndex('SEM_INSTRUMENT_NAME'),
      symbol: getColumnIndex('SM_SYMBOL_NAME'),
      displayName: getColumnIndex('SEM_CUSTOM_SYMBOL'),
      instrumentType: getColumnIndex('SEM_EXCH_INSTRUMENT_TYPE'),
      series: getColumnIndex('SEM_SERIES'),
      lotSize: getColumnIndex('SEM_LOT_UNITS'),
      tickSize: getColumnIndex('SEM_TICK_SIZE'),
      expiryDate: getColumnIndex('SEM_EXPIRY_DATE'),
      strikePrice: getColumnIndex('SEM_STRIKE_PRICE'),
      optionType: getColumnIndex('SEM_OPTION_TYPE'),
      expiryFlag: getColumnIndex('SEM_EXPIRY_FLAG')
    };

    const instruments: ParsedInstrument[] = [];

    for (let i = 1; i < lines.length; i++) {
      const row = lines[i].split(',');
      if (row.length < headers.length) continue;

      try {
        const instrument: ParsedInstrument = {
          securityId: i.toString(), // Row number as security ID
          symbol: row[indices.symbol]?.trim() || '',
          displayName: row[indices.displayName]?.trim() || '',
          exchange: row[indices.exchange]?.trim() || '',
          segment: row[indices.segment]?.trim() || '',
          instrumentType: row[indices.instrumentType]?.trim() || '',
          series: row[indices.series]?.trim() || '',
          lotSize: parseInt(row[indices.lotSize]?.trim() || '1'),
          tickSize: parseFloat(row[indices.tickSize]?.trim() || '0.01')
        };

        // Add optional fields for derivatives
        if (indices.expiryDate >= 0 && row[indices.expiryDate]?.trim()) {
          instrument.expiryDate = row[indices.expiryDate].trim();
        }
        if (indices.strikePrice >= 0 && row[indices.strikePrice]?.trim()) {
          instrument.strikePrice = parseFloat(row[indices.strikePrice].trim());
        }
        if (indices.optionType >= 0 && row[indices.optionType]?.trim()) {
          instrument.optionType = row[indices.optionType].trim() as 'CE' | 'PE';
        }
        if (indices.expiryFlag >= 0 && row[indices.expiryFlag]?.trim()) {
          instrument.expiryFlag = row[indices.expiryFlag].trim() as 'M' | 'W';
        }

        // Only add instruments with valid data
        if (instrument.symbol && instrument.exchange) {
          instruments.push(instrument);
        }
      } catch (e) {
        // Skip malformed rows
        continue;
      }
    }

    return instruments;
  }

  /**
   * Search instruments by symbol or display name
   */
  static async searchInstruments(
    query: string, 
    exchange?: string, 
    segment?: string,
    limit: number = 20
  ): Promise<ParsedInstrument[]> {
    const instruments = await this.fetchInstruments();
    
    if (!query.trim()) {
      return instruments
        .filter(inst => {
          if (exchange && inst.exchange !== exchange) return false;
          if (segment && inst.segment !== segment) return false;
          return true;
        })
        .slice(0, limit);
    }

    const searchTerm = query.toLowerCase().trim();
    
    return instruments
      .filter(inst => {
        // Filter by exchange and segment if specified
        if (exchange && inst.exchange !== exchange) return false;
        if (segment && inst.segment !== segment) return false;
        
        // Search in symbol and display name
        return inst.symbol.toLowerCase().includes(searchTerm) ||
               inst.displayName.toLowerCase().includes(searchTerm);
      })
      .sort((a, b) => {
        // Prioritize exact matches
        const aSymbolMatch = a.symbol.toLowerCase() === searchTerm;
        const bSymbolMatch = b.symbol.toLowerCase() === searchTerm;
        if (aSymbolMatch && !bSymbolMatch) return -1;
        if (!aSymbolMatch && bSymbolMatch) return 1;
        
        // Then prioritize symbol starts with
        const aSymbolStarts = a.symbol.toLowerCase().startsWith(searchTerm);
        const bSymbolStarts = b.symbol.toLowerCase().startsWith(searchTerm);
        if (aSymbolStarts && !bSymbolStarts) return -1;
        if (!aSymbolStarts && bSymbolStarts) return 1;
        
        // Finally sort alphabetically
        return a.symbol.localeCompare(b.symbol);
      })
      .slice(0, limit);
  }

  /**
   * Get instrument by security ID
   */
  static async getInstrumentById(securityId: string): Promise<ParsedInstrument | null> {
    const instruments = await this.fetchInstruments();
    return instruments.find(inst => inst.securityId === securityId) || null;
  }

  /**
   * Get popular instruments for quick access
   */
  static async getPopularInstruments(): Promise<ParsedInstrument[]> {
    const instruments = await this.fetchInstruments();
    
    // Popular NSE equity symbols
    const popularSymbols = [
      'RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'HINDUNILVR',
      'ICICIBANK', 'KOTAKBANK', 'BHARTIARTL', 'ITC', 'SBIN',
      'BAJFINANCE', 'ASIANPAINT', 'MARUTI', 'AXISBANK', 'LT'
    ];

    return instruments
      .filter(inst => 
        inst.exchange === 'NSE' && 
        inst.segment === 'E' && 
        popularSymbols.includes(inst.symbol)
      )
      .sort((a, b) => {
        const aIndex = popularSymbols.indexOf(a.symbol);
        const bIndex = popularSymbols.indexOf(b.symbol);
        return aIndex - bIndex;
      });
  }

  /**
   * Clear cache
   */
  static clearCache(): void {
    this.instruments = [];
    this.lastFetchTime = 0;
    try {
      localStorage.removeItem('dhan_instruments');
    } catch (e) {
      // Ignore errors
    }
  }
}

export default InstrumentService;
