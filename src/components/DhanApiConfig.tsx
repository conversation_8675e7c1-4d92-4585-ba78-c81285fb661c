import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Input,
  Card,
  CardBody,
  Chip,
  Divider,
  Link,
  Textarea,
  Spinner
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { DhanApiService, DhanProfile } from '../services/dhanApiService';

interface DhanApiConfigProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  onSuccess?: () => void;
}

export const DhanApiConfig: React.FC<DhanApiConfigProps> = ({
  isOpen,
  onOpenChange,
  onSuccess
}) => {
  const [accessToken, setAccessToken] = React.useState('');
  const [isConnecting, setIsConnecting] = React.useState(false);
  const [connectionStatus, setConnectionStatus] = React.useState<{
    success: boolean;
    message: string;
    profile?: <PERSON>han<PERSON>ro<PERSON>le;
  } | null>(null);
  const [isConnected, setIsConnected] = React.useState(false);

  // Load existing token on mount
  React.useEffect(() => {
    const existingToken = DhanApiService.getAccessToken();
    if (existingToken) {
      setAccessToken(existingToken);
      setIsConnected(true);
    }
  }, []);

  const handleTestConnection = async () => {
    if (!accessToken.trim()) {
      setConnectionStatus({
        success: false,
        message: 'Please enter your access token'
      });
      return;
    }

    setIsConnecting(true);
    setConnectionStatus(null);

    try {
      // Set the token temporarily for testing
      DhanApiService.setAccessToken(accessToken.trim());
      
      const result = await DhanApiService.testConnection();
      setConnectionStatus(result);
      
      if (result.success) {
        setIsConnected(true);
      } else {
        // Clear token if connection failed
        DhanApiService.clearAccessToken();
        setIsConnected(false);
      }
    } catch (error) {
      setConnectionStatus({
        success: false,
        message: error instanceof Error ? error.message : 'Connection failed'
      });
      DhanApiService.clearAccessToken();
      setIsConnected(false);
    } finally {
      setIsConnecting(false);
    }
  };

  const handleSave = () => {
    if (isConnected && accessToken.trim()) {
      DhanApiService.setAccessToken(accessToken.trim());
      onOpenChange(false);
      if (onSuccess) {
        onSuccess();
      }
    }
  };

  const handleDisconnect = () => {
    DhanApiService.clearAccessToken();
    setAccessToken('');
    setIsConnected(false);
    setConnectionStatus(null);
  };

  const handleClose = () => {
    // Reset form if not connected
    if (!isConnected) {
      setAccessToken('');
      setConnectionStatus(null);
    }
    onOpenChange(false);
  };

  return (
    <Modal 
      isOpen={isOpen} 
      onOpenChange={handleClose}
      size="2xl"
      scrollBehavior="inside"
      classNames={{
        base: "max-h-[90vh]",
        body: "py-6",
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <Icon icon="lucide:link" className="w-5 h-5" />
            <span>Dhan API Configuration</span>
          </div>
          <p className="text-sm text-foreground-500 font-normal">
            Connect your Dhan trading account to import trades automatically
          </p>
        </ModalHeader>

        <ModalBody>
          {/* Instructions Card */}
          <Card className="bg-primary-50 dark:bg-primary-950 border border-primary-200 dark:border-primary-800">
            <CardBody className="gap-3">
              <div className="flex items-center gap-2">
                <Icon icon="lucide:info" className="w-4 h-4 text-primary-600" />
                <span className="font-medium text-primary-700 dark:text-primary-300">
                  How to get your Dhan Access Token:
                </span>
              </div>
              <ol className="text-sm text-primary-600 dark:text-primary-400 space-y-1 ml-4">
                <li>1. Login to <Link href="https://web.dhan.co" target="_blank" className="text-primary-600 underline">web.dhan.co</Link></li>
                <li>2. Click on "My Profile" in the top menu</li>
                <li>3. Navigate to "Access DhanHQ APIs"</li>
                <li>4. Copy your Access Token</li>
                <li>5. Paste it below and test the connection</li>
              </ol>
            </CardBody>
          </Card>

          {/* Access Token Input */}
          <div className="space-y-4">
            <Textarea
              label="Dhan Access Token"
              placeholder="Enter your Dhan API access token (JWT)"
              value={accessToken}
              onValueChange={setAccessToken}
              variant="bordered"
              minRows={3}
              maxRows={5}
              description="Your access token will be stored securely in your browser"
              classNames={{
                input: "font-mono text-sm"
              }}
            />

            {/* Test Connection Button */}
            <Button
              color="primary"
              variant="solid"
              onPress={handleTestConnection}
              isLoading={isConnecting}
              isDisabled={!accessToken.trim() || isConnecting}
              startContent={!isConnecting && <Icon icon="lucide:wifi" className="w-4 h-4" />}
              className="w-full"
            >
              {isConnecting ? 'Testing Connection...' : 'Test Connection'}
            </Button>

            {/* Connection Status */}
            {connectionStatus && (
              <Card className={`border ${
                connectionStatus.success 
                  ? 'bg-success-50 border-success-200 dark:bg-success-950 dark:border-success-800' 
                  : 'bg-danger-50 border-danger-200 dark:bg-danger-950 dark:border-danger-800'
              }`}>
                <CardBody>
                  <div className="flex items-start gap-3">
                    <Icon 
                      icon={connectionStatus.success ? "lucide:check-circle" : "lucide:x-circle"} 
                      className={`w-5 h-5 mt-0.5 ${
                        connectionStatus.success ? 'text-success-600' : 'text-danger-600'
                      }`} 
                    />
                    <div className="flex-1">
                      <p className={`font-medium ${
                        connectionStatus.success ? 'text-success-700 dark:text-success-300' : 'text-danger-700 dark:text-danger-300'
                      }`}>
                        {connectionStatus.success ? 'Connection Successful!' : 'Connection Failed'}
                      </p>
                      <p className={`text-sm ${
                        connectionStatus.success ? 'text-success-600 dark:text-success-400' : 'text-danger-600 dark:text-danger-400'
                      }`}>
                        {connectionStatus.message}
                      </p>
                      
                      {/* Profile Information */}
                      {connectionStatus.success && connectionStatus.profile && (
                        <div className="mt-3 space-y-2">
                          <Divider />
                          <div className="grid grid-cols-2 gap-2 text-xs">
                            <div>
                              <span className="font-medium">Client ID:</span>
                              <p className="text-foreground-600">{connectionStatus.profile.dhanClientId}</p>
                            </div>
                            <div>
                              <span className="font-medium">Token Validity:</span>
                              <p className="text-foreground-600">{connectionStatus.profile.tokenValidity}</p>
                            </div>
                            <div>
                              <span className="font-medium">Active Segments:</span>
                              <p className="text-foreground-600">{connectionStatus.profile.activeSegment}</p>
                            </div>
                            <div>
                              <span className="font-medium">Data Plan:</span>
                              <Chip 
                                size="sm" 
                                color={connectionStatus.profile.dataPlan === 'Active' ? 'success' : 'warning'}
                                variant="flat"
                              >
                                {connectionStatus.profile.dataPlan}
                              </Chip>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </CardBody>
              </Card>
            )}

            {/* Current Connection Status */}
            {isConnected && !isConnecting && (
              <Card className="bg-success-50 border border-success-200 dark:bg-success-950 dark:border-success-800">
                <CardBody>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Icon icon="lucide:check-circle" className="w-4 h-4 text-success-600" />
                      <span className="text-success-700 dark:text-success-300 font-medium">
                        Connected to Dhan API
                      </span>
                    </div>
                    <Button
                      size="sm"
                      variant="light"
                      color="danger"
                      onPress={handleDisconnect}
                      startContent={<Icon icon="lucide:unlink" className="w-3 h-3" />}
                    >
                      Disconnect
                    </Button>
                  </div>
                </CardBody>
              </Card>
            )}
          </div>
        </ModalBody>

        <ModalFooter>
          <Button 
            variant="light" 
            onPress={handleClose}
          >
            Cancel
          </Button>
          <Button 
            color="primary" 
            onPress={handleSave}
            isDisabled={!isConnected}
            startContent={<Icon icon="lucide:save" className="w-4 h-4" />}
          >
            Save Configuration
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
