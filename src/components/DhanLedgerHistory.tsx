import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dalBody,
  <PERSON>dal<PERSON>ooter,
  <PERSON><PERSON>,
  Card,
  CardBody,
  Chip,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Spinner,

  DateRangePicker
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { DhanApiService, DhanLedgerEntry } from '../services/dhanApiService';
import { parseDate, getLocalTimeZone } from '@internationalized/date';

interface DhanLedgerHistoryProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
}

export const DhanLedgerHistory: React.FC<DhanLedgerHistoryProps> = ({
  isOpen,
  onOpenChange
}) => {
  const [isLoading, setIsLoading] = React.useState(false);
  const [ledgerEntries, setLedgerEntries] = React.useState<DhanLedgerEntry[]>([]);
  const [error, setError] = React.useState<string | null>(null);

  // Date range state (using the exact range from Dhan web that has data)
  const [dateRange, setDateRange] = React.useState(() => {
    // Use the exact date range from Dhan web: 6 June 2025 to 21 June 2025
    const startDate = new Date('2025-06-06');
    const endDate = new Date('2025-06-21');

    return {
      start: parseDate(startDate.toISOString().split('T')[0]),
      end: parseDate(endDate.toISOString().split('T')[0])
    };
  });

  const formatDateForAPI = (date: any) => {
    // Convert CalendarDate to YYYY-MM-DD format
    if (date && typeof date === 'object' && date.year && date.month && date.day) {
      const year = date.year;
      const month = String(date.month).padStart(2, '0');
      const day = String(date.day).padStart(2, '0');
      const formatted = `${year}-${month}-${day}`;
      console.log('🔍 Date formatting:', { input: date, output: formatted });
      return formatted;
    }

    // Fallback for other date formats
    if (date) {
      const fallback = date.toString();
      console.log('🔍 Date formatting fallback:', { input: date, output: fallback });
      return fallback;
    }

    console.error('❌ Invalid date object:', date);
    return '';
  };

  const fetchLedger = async () => {
    if (!dateRange.start || !dateRange.end) return;

    setIsLoading(true);
    setError(null);

    try {
      const fromDate = formatDateForAPI(dateRange.start);
      const toDate = formatDateForAPI(dateRange.end);

      console.log('🔍 Formatted dates for ledger API:', { fromDate, toDate });
      console.log('🔍 Raw date objects:', { start: dateRange.start, end: dateRange.end });

      const data = await DhanApiService.getLedger(fromDate, toDate);
      setLedgerEntries(Array.isArray(data) ? data : [data]);
    } catch (error) {
      console.error('❌ Ledger fetch error:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch ledger data');
    } finally {
      setIsLoading(false);
    }
  };



  const handleDateRangeChange = (range: any) => {
    setDateRange(range);
  };

  const handleFetchData = () => {
    fetchLedger();
  };

  // Fetch data when modal opens
  React.useEffect(() => {
    if (isOpen && dateRange.start && dateRange.end) {
      fetchLedger();
    }
  }, [isOpen]);

  const formatCurrency = (amount: string | number) => {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2
    }).format(numAmount);
  };



  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Calculate ledger summary
  const ledgerSummary = React.useMemo(() => {
    const totalCredits = ledgerEntries.reduce((sum, entry) => sum + parseFloat(entry.credit || '0'), 0);
    const totalDebits = ledgerEntries.reduce((sum, entry) => sum + parseFloat(entry.debit || '0'), 0);
    const netAmount = totalCredits - totalDebits;
    
    return { totalCredits, totalDebits, netAmount };
  }, [ledgerEntries]);



  return (
    <Modal 
      isOpen={isOpen} 
      onOpenChange={onOpenChange}
      size="5xl"
      scrollBehavior="inside"
      classNames={{
        base: "max-h-[90vh]",
        body: "py-6",
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <Icon icon="lucide:file-text" className="w-5 h-5" />
            <span>Account Ledger</span>
          </div>
          <p className="text-sm text-foreground-500 font-normal">
            View your account ledger and transaction history from Dhan
          </p>
        </ModalHeader>

        <ModalBody>
          {/* Date Range Picker */}
          <Card>
            <CardBody>
              <div className="flex items-center gap-4 flex-wrap">
                <DateRangePicker
                  label="Select Date Range"
                  value={dateRange}
                  onChange={handleDateRangeChange}
                  className="max-w-xs"
                />
                <Button
                  color="primary"
                  onPress={handleFetchData}
                  isLoading={isLoading}
                  startContent={!isLoading && <Icon icon="lucide:search" className="w-4 h-4" />}
                >
                  {isLoading ? 'Loading...' : 'Fetch Data'}
                </Button>

                {/* Quick date range buttons */}
                <div className="flex gap-2 flex-wrap">
                  <Button
                    size="sm"
                    variant="flat"
                    onPress={() => {
                      const today = new Date('2025-06-21');
                      setDateRange({
                        start: parseDate(today.toISOString().split('T')[0]),
                        end: parseDate(today.toISOString().split('T')[0])
                      });
                    }}
                  >
                    Today Only
                  </Button>
                  <Button
                    size="sm"
                    variant="flat"
                    onPress={() => {
                      const today = new Date('2025-06-21');
                      const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
                      setDateRange({
                        start: parseDate(lastWeek.toISOString().split('T')[0]),
                        end: parseDate(today.toISOString().split('T')[0])
                      });
                    }}
                  >
                    Last 7 Days
                  </Button>
                  <Button
                    size="sm"
                    variant="flat"
                    color="success"
                    onPress={() => {
                      setDateRange({
                        start: parseDate('2025-06-06'),
                        end: parseDate('2025-06-21')
                      });
                    }}
                  >
                    Jun 6-21 (Known Data)
                  </Button>
                </div>


              </div>
            </CardBody>
          </Card>

          {error && (
            <Card className="bg-danger-50 border border-danger-200 dark:bg-danger-950 dark:border-danger-800">
              <CardBody>
                <div className="flex items-center gap-3">
                  <Icon icon="lucide:alert-circle" className="w-5 h-5 text-danger-600" />
                  <div>
                    <p className="font-medium text-danger-700 dark:text-danger-300">
                      Failed to Load Data
                    </p>
                    <p className="text-sm text-danger-600 dark:text-danger-400">
                      {error}
                    </p>
                  </div>
                </div>
              </CardBody>
            </Card>
          )}

          {/* Ledger Summary */}
          {!error && ledgerEntries.length > 0 && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <Card>
                <CardBody className="text-center">
                  <p className="text-sm text-foreground-500">Total Credits</p>
                  <p className="text-lg font-semibold text-success">{formatCurrency(ledgerSummary.totalCredits)}</p>
                </CardBody>
              </Card>
              <Card>
                <CardBody className="text-center">
                  <p className="text-sm text-foreground-500">Total Debits</p>
                  <p className="text-lg font-semibold text-danger">{formatCurrency(ledgerSummary.totalDebits)}</p>
                </CardBody>
              </Card>
              <Card>
                <CardBody className="text-center">
                  <p className="text-sm text-foreground-500">Net Amount</p>
                  <p className={`text-lg font-semibold ${ledgerSummary.netAmount >= 0 ? 'text-success' : 'text-danger'}`}>
                    {formatCurrency(ledgerSummary.netAmount)}
                  </p>
                </CardBody>
              </Card>
              <Card>
                <CardBody className="text-center">
                  <p className="text-sm text-foreground-500">Transactions</p>
                  <p className="text-lg font-semibold text-primary">{ledgerEntries.length}</p>
                </CardBody>
              </Card>
            </div>
          )}

          {/* Ledger Table */}
          {!error && ledgerEntries.length > 0 && (
            <Card>
              <CardBody className="p-0">
                <Table removeWrapper>
                  <TableHeader>
                    <TableColumn>DATE</TableColumn>
                    <TableColumn>NARRATION</TableColumn>
                    <TableColumn>EXCHANGE</TableColumn>
                    <TableColumn>VOUCHER</TableColumn>
                    <TableColumn>DEBIT</TableColumn>
                    <TableColumn>CREDIT</TableColumn>
                    <TableColumn>BALANCE</TableColumn>
                  </TableHeader>
                  <TableBody>
                    {ledgerEntries.map((entry, index) => (
                      <TableRow key={`${entry.vouchernumber}-${index}`}>
                        <TableCell>{formatDate(entry.voucherdate)}</TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium">{entry.narration}</p>
                            <p className="text-xs text-foreground-500">{entry.voucherdesc}</p>
                          </div>
                        </TableCell>
                        <TableCell>{entry.exchange}</TableCell>
                        <TableCell className="text-xs">{entry.vouchernumber}</TableCell>
                        <TableCell>
                          {entry.debit !== '0.00' && entry.debit !== '0' ? (
                            <span className="text-danger font-medium">{formatCurrency(entry.debit)}</span>
                          ) : (
                            <span className="text-foreground-400">-</span>
                          )}
                        </TableCell>
                        <TableCell>
                          {entry.credit !== '0.00' && entry.credit !== '0' ? (
                            <span className="text-success font-medium">{formatCurrency(entry.credit)}</span>
                          ) : (
                            <span className="text-foreground-400">-</span>
                          )}
                        </TableCell>
                        <TableCell className="font-medium">{formatCurrency(entry.runbal)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardBody>
            </Card>
          )}

          {/* No Data State */}
          {!error && !isLoading && ledgerEntries.length === 0 && (
            <Card>
              <CardBody className="text-center py-8">
                <Icon icon="lucide:file-text" className="w-12 h-12 mx-auto text-foreground-400 mb-4" />
                <p className="text-foreground-500">No ledger entries found for the selected date range</p>
                <p className="text-sm text-foreground-400 mt-2">Try selecting a different date range or check if you have any transactions</p>
              </CardBody>
            </Card>
          )}

          {isLoading && (
            <div className="flex flex-col items-center justify-center py-8">
              <Spinner size="lg" />
              <p className="mt-4 text-foreground-500">Loading data...</p>
            </div>
          )}
        </ModalBody>

        <ModalFooter>
          <Button
            variant="light"
            onPress={handleFetchData}
            startContent={<Icon icon="lucide:refresh-cw" className="w-4 h-4" />}
            isDisabled={isLoading}
          >
            Refresh
          </Button>
          <Button variant="light" onPress={() => onOpenChange(false)}>
            Close
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
