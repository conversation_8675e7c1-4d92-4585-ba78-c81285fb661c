import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dalBody,
  <PERSON>dal<PERSON>oot<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardBody,
  Chip,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Spinner,
  Divider
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { DhanApiService, DhanForeverOrder } from '../services/dhanApiService';

interface DhanForeverOrdersProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
}

export const DhanForeverOrders: React.FC<DhanForeverOrdersProps> = ({
  isOpen,
  onOpenChange
}) => {
  const [isLoading, setIsLoading] = React.useState(false);
  const [foreverOrders, setForeverOrders] = React.useState<DhanForeverOrder[]>([]);
  const [error, setError] = React.useState<string | null>(null);

  const fetchForeverOrders = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const orders = await DhanApiService.getForeverOrders();
      setForeverOrders(orders);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to fetch forever orders');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch orders when modal opens
  React.useEffect(() => {
    if (isOpen) {
      fetchForeverOrders();
    }
  }, [isOpen]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'CONFIRM': return 'success';
      case 'PENDING': return 'warning';
      case 'REJECTED': return 'danger';
      case 'CANCELLED': return 'default';
      case 'TRADED': return 'primary';
      case 'EXPIRED': return 'danger';
      default: return 'default';
    }
  };

  const getTransactionColor = (type: string) => {
    return type === 'BUY' ? 'success' : 'danger';
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Modal 
      isOpen={isOpen} 
      onOpenChange={onOpenChange}
      size="5xl"
      scrollBehavior="inside"
      classNames={{
        base: "max-h-[90vh]",
        body: "py-6",
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <Icon icon="lucide:clock" className="w-5 h-5" />
            <span>Forever Orders</span>
          </div>
          <p className="text-sm text-foreground-500 font-normal">
            View all your active forever orders from Dhan
          </p>
        </ModalHeader>

        <ModalBody>
          {isLoading && (
            <div className="flex flex-col items-center justify-center py-8">
              <Spinner size="lg" />
              <p className="mt-4 text-foreground-500">Loading forever orders...</p>
            </div>
          )}

          {error && (
            <Card className="bg-danger-50 border border-danger-200 dark:bg-danger-950 dark:border-danger-800">
              <CardBody>
                <div className="flex items-center gap-3">
                  <Icon icon="lucide:alert-circle" className="w-5 h-5 text-danger-600" />
                  <div>
                    <p className="font-medium text-danger-700 dark:text-danger-300">
                      Failed to Load Forever Orders
                    </p>
                    <p className="text-sm text-danger-600 dark:text-danger-400">
                      {error}
                    </p>
                  </div>
                </div>
              </CardBody>
            </Card>
          )}

          {!isLoading && !error && (
            <>
              {/* Summary Card */}
              <Card>
                <CardBody>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Total Forever Orders: {foreverOrders.length}</p>
                      <p className="text-sm text-foreground-500">
                        Active: {foreverOrders.filter(o => o.orderStatus === 'CONFIRM').length} | 
                        Pending: {foreverOrders.filter(o => o.orderStatus === 'PENDING').length}
                      </p>
                    </div>
                    <Button
                      size="sm"
                      variant="light"
                      onPress={fetchForeverOrders}
                      startContent={<Icon icon="lucide:refresh-cw" className="w-4 h-4" />}
                    >
                      Refresh
                    </Button>
                  </div>
                </CardBody>
              </Card>

              {/* Orders Table */}
              {foreverOrders.length > 0 ? (
                <Card>
                  <CardBody className="p-0">
                    <Table removeWrapper>
                      <TableHeader>
                        <TableColumn>SYMBOL</TableColumn>
                        <TableColumn>TYPE</TableColumn>
                        <TableColumn>ORDER TYPE</TableColumn>
                        <TableColumn>QUANTITY</TableColumn>
                        <TableColumn>PRICE</TableColumn>
                        <TableColumn>TRIGGER</TableColumn>
                        <TableColumn>STATUS</TableColumn>
                        <TableColumn>LEG</TableColumn>
                        <TableColumn>CREATED</TableColumn>
                      </TableHeader>
                      <TableBody>
                        {foreverOrders.map((order) => (
                          <TableRow key={order.orderId}>
                            <TableCell>
                              <div>
                                <p className="font-medium">{order.tradingSymbol}</p>
                                <p className="text-xs text-foreground-500">{order.exchangeSegment}</p>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Chip 
                                size="sm" 
                                color={getTransactionColor(order.transactionType)}
                                variant="flat"
                              >
                                {order.transactionType}
                              </Chip>
                            </TableCell>
                            <TableCell>
                              <div>
                                <p className="text-sm">{order.orderType}</p>
                                <p className="text-xs text-foreground-500">{order.productType}</p>
                              </div>
                            </TableCell>
                            <TableCell>{order.quantity.toLocaleString()}</TableCell>
                            <TableCell>{formatCurrency(order.price)}</TableCell>
                            <TableCell>{formatCurrency(order.triggerPrice)}</TableCell>
                            <TableCell>
                              <Chip 
                                size="sm" 
                                color={getStatusColor(order.orderStatus)}
                                variant="flat"
                              >
                                {order.orderStatus}
                              </Chip>
                            </TableCell>
                            <TableCell>
                              <Chip 
                                size="sm" 
                                color={order.legName === 'ENTRY_LEG' ? 'primary' : 'secondary'}
                                variant="flat"
                              >
                                {order.legName.replace('_', ' ')}
                              </Chip>
                            </TableCell>
                            <TableCell>
                              <p className="text-sm">{formatDate(order.createTime)}</p>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </CardBody>
                </Card>
              ) : (
                !isLoading && (
                  <Card>
                    <CardBody className="text-center py-8">
                      <Icon icon="lucide:clock" className="w-12 h-12 mx-auto text-foreground-400 mb-4" />
                      <p className="text-foreground-500">No forever orders found</p>
                      <p className="text-sm text-foreground-400 mt-2">
                        Your active forever orders will appear here
                      </p>
                    </CardBody>
                  </Card>
                )
              )}
            </>
          )}
        </ModalBody>

        <ModalFooter>
          <Button variant="light" onPress={() => onOpenChange(false)}>
            Close
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
