import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dalBody,
  <PERSON>dal<PERSON>ooter,
  <PERSON><PERSON>,
  Card,
  CardBody,
  Chip,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Spinner,
  Tabs,
  Tab,
  Divider
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { DhanApiService, DhanHolding, DhanPosition } from '../services/dhanApiService';

interface DhanHoldingsPositionsProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
}

export const DhanHoldingsPositions: React.FC<DhanHoldingsPositionsProps> = ({
  isOpen,
  onOpenChange
}) => {
  const [isLoading, setIsLoading] = React.useState(false);
  const [holdings, setHoldings] = React.useState<DhanHolding[]>([]);
  const [positions, setPositions] = React.useState<DhanPosition[]>([]);
  const [error, setError] = React.useState<string | null>(null);
  const [activeTab, setActiveTab] = React.useState('holdings');

  const fetchData = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const [holdingsData, positionsData] = await Promise.all([
        DhanApiService.getHoldings(),
        DhanApiService.getPositions()
      ]);
      
      setHoldings(holdingsData);
      setPositions(positionsData);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to fetch data');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data when modal opens
  React.useEffect(() => {
    if (isOpen) {
      fetchData();
    }
  }, [isOpen]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const getPositionTypeColor = (type: string) => {
    switch (type) {
      case 'LONG': return 'success';
      case 'SHORT': return 'danger';
      case 'CLOSED': return 'default';
      default: return 'default';
    }
  };

  const getProfitColor = (profit: number) => {
    return profit >= 0 ? 'success' : 'danger';
  };

  // Calculate totals
  const holdingsTotalValue = holdings.reduce((sum, h) => sum + (h.totalQty * h.avgCostPrice), 0);
  const positionsTotalPnL = positions.reduce((sum, p) => sum + p.unrealizedProfit, 0);
  const positionsRealizedPnL = positions.reduce((sum, p) => sum + p.realizedProfit, 0);

  return (
    <Modal 
      isOpen={isOpen} 
      onOpenChange={onOpenChange}
      size="5xl"
      scrollBehavior="inside"
      classNames={{
        base: "max-h-[90vh]",
        body: "py-6",
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <Icon icon="lucide:briefcase" className="w-5 h-5" />
            <span>Holdings & Positions</span>
          </div>
          <p className="text-sm text-foreground-500 font-normal">
            View your holdings and open positions from Dhan
          </p>
        </ModalHeader>

        <ModalBody>
          {isLoading && (
            <div className="flex flex-col items-center justify-center py-8">
              <Spinner size="lg" />
              <p className="mt-4 text-foreground-500">Loading data...</p>
            </div>
          )}

          {error && (
            <Card className="bg-danger-50 border border-danger-200 dark:bg-danger-950 dark:border-danger-800">
              <CardBody>
                <div className="flex items-center gap-3">
                  <Icon icon="lucide:alert-circle" className="w-5 h-5 text-danger-600" />
                  <div>
                    <p className="font-medium text-danger-700 dark:text-danger-300">
                      Failed to Load Data
                    </p>
                    <p className="text-sm text-danger-600 dark:text-danger-400">
                      {error}
                    </p>
                  </div>
                </div>
              </CardBody>
            </Card>
          )}

          {!isLoading && !error && (
            <>
              {/* Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <Card>
                  <CardBody className="text-center">
                    <p className="text-sm text-foreground-500">Holdings Value</p>
                    <p className="text-xl font-semibold text-primary">{formatCurrency(holdingsTotalValue)}</p>
                    <p className="text-xs text-foreground-400">{holdings.length} holdings</p>
                  </CardBody>
                </Card>
                
                <Card>
                  <CardBody className="text-center">
                    <p className="text-sm text-foreground-500">Unrealized P&L</p>
                    <p className={`text-xl font-semibold ${positionsTotalPnL >= 0 ? 'text-success' : 'text-danger'}`}>
                      {formatCurrency(positionsTotalPnL)}
                    </p>
                    <p className="text-xs text-foreground-400">{positions.length} positions</p>
                  </CardBody>
                </Card>
                
                <Card>
                  <CardBody className="text-center">
                    <p className="text-sm text-foreground-500">Realized P&L</p>
                    <p className={`text-xl font-semibold ${positionsRealizedPnL >= 0 ? 'text-success' : 'text-danger'}`}>
                      {formatCurrency(positionsRealizedPnL)}
                    </p>
                    <p className="text-xs text-foreground-400">Today</p>
                  </CardBody>
                </Card>
              </div>

              {/* Tabs */}
              <Tabs 
                selectedKey={activeTab} 
                onSelectionChange={(key) => setActiveTab(key as string)}
                className="w-full"
              >
                <Tab key="holdings" title={`Holdings (${holdings.length})`}>
                  {holdings.length > 0 ? (
                    <Card>
                      <CardBody className="p-0">
                        <Table removeWrapper>
                          <TableHeader>
                            <TableColumn>SYMBOL</TableColumn>
                            <TableColumn>EXCHANGE</TableColumn>
                            <TableColumn>TOTAL QTY</TableColumn>
                            <TableColumn>AVAILABLE QTY</TableColumn>
                            <TableColumn>AVG COST</TableColumn>
                            <TableColumn>TOTAL VALUE</TableColumn>
                            <TableColumn>T1 QTY</TableColumn>
                          </TableHeader>
                          <TableBody>
                            {holdings.map((holding, index) => (
                              <TableRow key={`${holding.securityId}-${index}`}>
                                <TableCell>
                                  <div>
                                    <p className="font-medium">{holding.tradingSymbol}</p>
                                    <p className="text-xs text-foreground-500">{holding.isin}</p>
                                  </div>
                                </TableCell>
                                <TableCell>{holding.exchange}</TableCell>
                                <TableCell>{holding.totalQty.toLocaleString()}</TableCell>
                                <TableCell>{holding.availableQty.toLocaleString()}</TableCell>
                                <TableCell>{formatCurrency(holding.avgCostPrice)}</TableCell>
                                <TableCell className="font-medium">
                                  {formatCurrency(holding.totalQty * holding.avgCostPrice)}
                                </TableCell>
                                <TableCell>
                                  {holding.t1Qty > 0 ? (
                                    <Chip size="sm" color="warning" variant="flat">
                                      {holding.t1Qty}
                                    </Chip>
                                  ) : (
                                    <span className="text-foreground-400">-</span>
                                  )}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </CardBody>
                    </Card>
                  ) : (
                    <Card>
                      <CardBody className="text-center py-8">
                        <Icon icon="lucide:briefcase" className="w-12 h-12 mx-auto text-foreground-400 mb-4" />
                        <p className="text-foreground-500">No holdings found</p>
                      </CardBody>
                    </Card>
                  )}
                </Tab>

                <Tab key="positions" title={`Positions (${positions.length})`}>
                  {positions.length > 0 ? (
                    <Card>
                      <CardBody className="p-0">
                        <Table removeWrapper>
                          <TableHeader>
                            <TableColumn>SYMBOL</TableColumn>
                            <TableColumn>TYPE</TableColumn>
                            <TableColumn>NET QTY</TableColumn>
                            <TableColumn>AVG PRICE</TableColumn>
                            <TableColumn>UNREALIZED P&L</TableColumn>
                            <TableColumn>REALIZED P&L</TableColumn>
                            <TableColumn>PRODUCT</TableColumn>
                          </TableHeader>
                          <TableBody>
                            {positions.map((position, index) => (
                              <TableRow key={`${position.securityId}-${index}`}>
                                <TableCell>
                                  <div>
                                    <p className="font-medium">{position.tradingSymbol}</p>
                                    <p className="text-xs text-foreground-500">{position.exchangeSegment}</p>
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <Chip 
                                    size="sm" 
                                    color={getPositionTypeColor(position.positionType)}
                                    variant="flat"
                                  >
                                    {position.positionType}
                                  </Chip>
                                </TableCell>
                                <TableCell>{position.netQty.toLocaleString()}</TableCell>
                                <TableCell>
                                  {position.positionType === 'LONG' 
                                    ? formatCurrency(position.buyAvg)
                                    : formatCurrency(position.sellAvg)
                                  }
                                </TableCell>
                                <TableCell>
                                  <span className={`font-medium ${position.unrealizedProfit >= 0 ? 'text-success' : 'text-danger'}`}>
                                    {formatCurrency(position.unrealizedProfit)}
                                  </span>
                                </TableCell>
                                <TableCell>
                                  <span className={`font-medium ${position.realizedProfit >= 0 ? 'text-success' : 'text-danger'}`}>
                                    {formatCurrency(position.realizedProfit)}
                                  </span>
                                </TableCell>
                                <TableCell>
                                  <Chip size="sm" variant="flat">
                                    {position.productType}
                                  </Chip>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </CardBody>
                    </Card>
                  ) : (
                    <Card>
                      <CardBody className="text-center py-8">
                        <Icon icon="lucide:trending-up" className="w-12 h-12 mx-auto text-foreground-400 mb-4" />
                        <p className="text-foreground-500">No open positions</p>
                      </CardBody>
                    </Card>
                  )}
                </Tab>
              </Tabs>
            </>
          )}
        </ModalBody>

        <ModalFooter>
          <Button
            variant="light"
            onPress={fetchData}
            startContent={<Icon icon="lucide:refresh-cw" className="w-4 h-4" />}
            isDisabled={isLoading}
          >
            Refresh
          </Button>
          <Button variant="light" onPress={() => onOpenChange(false)}>
            Close
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
