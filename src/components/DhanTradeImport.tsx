import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ead<PERSON>,
  <PERSON>dalBody,
  <PERSON>dal<PERSON>ooter,
  <PERSON><PERSON>,
  Card,
  CardBody,
  Progress,
  Chip,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Checkbox,
  Divider
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { DhanApiService, DhanTrade } from '../services/dhanApiService';
import { Trade } from '../types/trade';

interface DhanTradeImportProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  onImport: (trades: Partial<Trade>[]) => void;
  portfolioSize: number;
}

export const DhanTradeImport: React.FC<DhanTradeImportProps> = ({
  isOpen,
  onOpenChange,
  onImport,
  portfolioSize
}) => {
  const [isLoading, setIsLoading] = React.useState(false);
  const [dhanTrades, setDhanTrades] = React.useState<DhanTrade[]>([]);
  const [convertedTrades, setConvertedTrades] = React.useState<Partial<Trade>[]>([]);
  const [selectedTrades, setSelectedTrades] = React.useState<Set<number>>(new Set());
  const [error, setError] = React.useState<string | null>(null);
  const [step, setStep] = React.useState<'fetch' | 'review' | 'importing'>('fetch');

  const fetchTrades = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const trades = await DhanApiService.getTrades();
      setDhanTrades(trades);
      
      // Convert to journal format
      const converted = DhanApiService.convertDhanTradesToJournalTrades(trades, portfolioSize);
      setConvertedTrades(converted);
      
      // Select all trades by default
      setSelectedTrades(new Set(converted.map((_, index) => index)));
      
      setStep('review');
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to fetch trades');
    } finally {
      setIsLoading(false);
    }
  };

  const handleImport = async () => {
    setStep('importing');
    
    try {
      const tradesToImport = convertedTrades.filter((_, index) => selectedTrades.has(index));
      await onImport(tradesToImport);
      onOpenChange(false);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to import trades');
      setStep('review');
    }
  };

  const handleClose = () => {
    setStep('fetch');
    setDhanTrades([]);
    setConvertedTrades([]);
    setSelectedTrades(new Set());
    setError(null);
    onOpenChange(false);
  };

  const toggleTradeSelection = (index: number) => {
    const newSelection = new Set(selectedTrades);
    if (newSelection.has(index)) {
      newSelection.delete(index);
    } else {
      newSelection.add(index);
    }
    setSelectedTrades(newSelection);
  };

  const selectAllTrades = () => {
    setSelectedTrades(new Set(convertedTrades.map((_, index) => index)));
  };

  const deselectAllTrades = () => {
    setSelectedTrades(new Set());
  };

  return (
    <Modal 
      isOpen={isOpen} 
      onOpenChange={handleClose}
      size="5xl"
      scrollBehavior="inside"
      classNames={{
        base: "max-h-[90vh]",
        body: "py-6",
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <Icon icon="lucide:download" className="w-5 h-5" />
            <span>Import Trades from Dhan</span>
          </div>
          <p className="text-sm text-foreground-500 font-normal">
            Import your trades from Dhan trading platform
          </p>
        </ModalHeader>

        <ModalBody>
          {step === 'fetch' && (
            <div className="space-y-6">
              {/* Connection Status */}
              <Card className="bg-primary-50 dark:bg-primary-950 border border-primary-200 dark:border-primary-800">
                <CardBody>
                  <div className="flex items-center gap-3">
                    <Icon icon="lucide:info" className="w-5 h-5 text-primary-600" />
                    <div>
                      <p className="font-medium text-primary-700 dark:text-primary-300">
                        Ready to import trades from Dhan
                      </p>
                      <p className="text-sm text-primary-600 dark:text-primary-400">
                        This will fetch all trades from today and convert them to your journal format
                      </p>
                    </div>
                  </div>
                </CardBody>
              </Card>

              {error && (
                <Card className="bg-danger-50 border border-danger-200 dark:bg-danger-950 dark:border-danger-800">
                  <CardBody>
                    <div className="flex items-center gap-3">
                      <Icon icon="lucide:alert-circle" className="w-5 h-5 text-danger-600" />
                      <div>
                        <p className="font-medium text-danger-700 dark:text-danger-300">
                          Import Failed
                        </p>
                        <p className="text-sm text-danger-600 dark:text-danger-400">
                          {error}
                        </p>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              )}

              <Button
                color="primary"
                size="lg"
                onPress={fetchTrades}
                isLoading={isLoading}
                startContent={!isLoading && <Icon icon="lucide:download" className="w-5 h-5" />}
                className="w-full"
              >
                {isLoading ? 'Fetching Trades...' : 'Fetch Trades from Dhan'}
              </Button>
            </div>
          )}

          {step === 'review' && (
            <div className="space-y-6">
              {/* Summary */}
              <Card>
                <CardBody>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Found {convertedTrades.length} trades to import</p>
                      <p className="text-sm text-foreground-500">
                        {selectedTrades.size} selected for import
                      </p>
                    </div>
                    <div className="flex gap-2">
                      <Button size="sm" variant="light" onPress={selectAllTrades}>
                        Select All
                      </Button>
                      <Button size="sm" variant="light" onPress={deselectAllTrades}>
                        Deselect All
                      </Button>
                    </div>
                  </div>
                </CardBody>
              </Card>

              {/* Trades Table */}
              {convertedTrades.length > 0 && (
                <Card>
                  <CardBody className="p-0">
                    <Table removeWrapper>
                      <TableHeader>
                        <TableColumn>SELECT</TableColumn>
                        <TableColumn>SYMBOL</TableColumn>
                        <TableColumn>DATE</TableColumn>
                        <TableColumn>TYPE</TableColumn>
                        <TableColumn>ENTRY</TableColumn>
                        <TableColumn>QUANTITY</TableColumn>
                        <TableColumn>POSITION SIZE</TableColumn>
                        <TableColumn>STATUS</TableColumn>
                      </TableHeader>
                      <TableBody>
                        {convertedTrades.map((trade, index) => (
                          <TableRow key={index}>
                            <TableCell>
                              <Checkbox
                                isSelected={selectedTrades.has(index)}
                                onValueChange={() => toggleTradeSelection(index)}
                              />
                            </TableCell>
                            <TableCell className="font-medium">{trade.name}</TableCell>
                            <TableCell>{trade.date}</TableCell>
                            <TableCell>
                              <Chip 
                                size="sm" 
                                color={trade.buySell === 'Buy' ? 'success' : 'danger'}
                                variant="flat"
                              >
                                {trade.buySell}
                              </Chip>
                            </TableCell>
                            <TableCell>₹{trade.avgEntry?.toFixed(2)}</TableCell>
                            <TableCell>{trade.initialQty}</TableCell>
                            <TableCell>₹{trade.positionSize?.toLocaleString()}</TableCell>
                            <TableCell>
                              <Chip 
                                size="sm" 
                                color={
                                  trade.positionStatus === 'Open' ? 'primary' :
                                  trade.positionStatus === 'Closed' ? 'success' : 'warning'
                                }
                                variant="flat"
                              >
                                {trade.positionStatus}
                              </Chip>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </CardBody>
                </Card>
              )}

              {convertedTrades.length === 0 && (
                <Card>
                  <CardBody className="text-center py-8">
                    <Icon icon="lucide:inbox" className="w-12 h-12 mx-auto text-foreground-400 mb-4" />
                    <p className="text-foreground-500">No trades found for today</p>
                  </CardBody>
                </Card>
              )}
            </div>
          )}

          {step === 'importing' && (
            <div className="space-y-6 text-center">
              <div className="flex flex-col items-center gap-4">
                <Icon icon="lucide:upload" className="w-12 h-12 text-primary-500 animate-pulse" />
                <div>
                  <p className="font-medium">Importing Trades...</p>
                  <p className="text-sm text-foreground-500">
                    Adding {selectedTrades.size} trades to your journal
                  </p>
                </div>
              </div>
              <Progress isIndeterminate color="primary" />
            </div>
          )}
        </ModalBody>

        <ModalFooter>
          {step === 'fetch' && (
            <Button variant="light" onPress={handleClose}>
              Cancel
            </Button>
          )}
          
          {step === 'review' && (
            <>
              <Button variant="light" onPress={() => setStep('fetch')}>
                Back
              </Button>
              <Button 
                color="primary" 
                onPress={handleImport}
                isDisabled={selectedTrades.size === 0}
                startContent={<Icon icon="lucide:upload" className="w-4 h-4" />}
              >
                Import {selectedTrades.size} Trades
              </Button>
            </>
          )}
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
