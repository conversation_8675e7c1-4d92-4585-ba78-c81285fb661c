import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ody,
  <PERSON><PERSON>,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Chip,
  Input,
  Spinner,
  Card,
  CardBody,
  Pagination
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { DhanApiService, DhanTradeHistory } from '../services/dhanApiService';

interface DhanTradeHistoryModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export const DhanTradeHistoryModal: React.FC<DhanTradeHistoryModalProps> = ({
  isOpen,
  onOpenChange
}) => {
  // Date range state (default to last 30 days)
  const today = new Date();
  const thirtyDaysAgo = new Date(today);
  thirtyDaysAgo.setDate(today.getDate() - 30);

  const [fromDate, setFromDate] = React.useState(thirtyDaysAgo.toISOString().split('T')[0]);
  const [toDate, setToDate] = React.useState(today.toISOString().split('T')[0]);

  const [tradeHistory, setTradeHistory] = React.useState<DhanTradeHistory[]>([]);
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [currentPage, setCurrentPage] = React.useState(0);
  const [totalPages, setTotalPages] = React.useState(1);

  const fetchTradeHistory = async (page: number = 0) => {
    if (!fromDate || !toDate) return;

    setIsLoading(true);
    setError(null);

    try {
      console.log('🔍 Fetching trade history:', { fromDate, toDate, page });

      const data = await DhanApiService.getTradeHistory(fromDate, toDate, page);
      const dataArray = Array.isArray(data) ? data : [data];
      setTradeHistory(dataArray);
      setCurrentPage(page);

      // Better pagination logic
      if (dataArray.length === 0) {
        // No data on this page
        if (page === 0) {
          setTotalPages(1); // No data at all
        } else {
          setTotalPages(page); // This page is empty, so previous page was the last
        }
      } else {
        // We have data, so there might be more pages
        // Assume there might be more pages unless we get less than expected
        const expectedPageSize = 50; // Dhan API typical page size
        if (dataArray.length < expectedPageSize) {
          // This is likely the last page
          setTotalPages(page + 1);
        } else {
          // There might be more pages, set total to at least current + 2
          setTotalPages(Math.max(page + 2, totalPages));
        }
      }
    } catch (error) {
      console.error('❌ Trade history fetch error:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch trade history');
    } finally {
      setIsLoading(false);
    }
  };

  const handleFetchData = () => {
    fetchTradeHistory(0);
    setCurrentPage(0);
  };

  const handlePageChange = (page: number) => {
    fetchTradeHistory(page);
  };

  const fetchAllPages = async () => {
    if (!fromDate || !toDate) return;

    setIsLoading(true);
    setError(null);

    try {
      console.log('🔍 Fetching ALL trade history pages:', { fromDate, toDate });

      let allTrades: any[] = [];
      let currentPage = 0;
      let hasMoreData = true;
      let consecutiveEmptyPages = 0;
      const maxEmptyPages = 3; // Stop after 3 consecutive empty pages
      const maxPages = 100; // Safety limit to prevent infinite loops

      while (hasMoreData && currentPage < maxPages) {
        console.log(`📄 Fetching page ${currentPage}...`);

        try {
          const data = await DhanApiService.getTradeHistory(fromDate, toDate, currentPage);
          const dataArray = Array.isArray(data) ? data : (data ? [data] : []);

          console.log(`📊 Page ${currentPage}: Received ${dataArray.length} trades`);

          if (dataArray.length === 0) {
            consecutiveEmptyPages++;
            console.log(`⚠️ Empty page ${currentPage} (${consecutiveEmptyPages}/${maxEmptyPages} consecutive empty pages)`);

            if (consecutiveEmptyPages >= maxEmptyPages) {
              console.log('🛑 Stopping: Too many consecutive empty pages');
              hasMoreData = false;
            }
          } else {
            consecutiveEmptyPages = 0; // Reset counter when we get data
            allTrades = [...allTrades, ...dataArray];

            // Log some sample data for debugging
            if (dataArray.length > 0) {
              const firstTrade = dataArray[0];
              console.log(`📈 Sample trade from page ${currentPage}:`, {
                symbol: firstTrade.customSymbol || firstTrade.tradingSymbol,
                date: firstTrade.exchangeTime,
                type: firstTrade.transactionType,
                price: firstTrade.tradedPrice
              });
            }
          }

          currentPage++;

          // Add a small delay to avoid overwhelming the API
          if (currentPage % 5 === 0) {
            console.log(`⏸️ Brief pause after ${currentPage} pages...`);
            await new Promise(resolve => setTimeout(resolve, 100));
          }

        } catch (pageError) {
          console.error(`❌ Error fetching page ${currentPage}:`, pageError);
          consecutiveEmptyPages++;

          if (consecutiveEmptyPages >= maxEmptyPages) {
            console.log('🛑 Stopping due to consecutive errors');
            hasMoreData = false;
          } else {
            currentPage++;
          }
        }
      }

      if (currentPage >= maxPages) {
        console.log(`⚠️ Reached maximum page limit (${maxPages})`);
      }

      console.log(`✅ Fetched ${allTrades.length} trades across ${currentPage} pages`);

      if (allTrades.length === 0) {
        setError(`No trades found for the date range ${fromDate} to ${toDate}. The API might not have data for this period.`);
      }

      setTradeHistory(allTrades);
      setCurrentPage(0);
      setTotalPages(1); // All data is now on one "virtual" page
    } catch (error) {
      console.error('❌ Fetch all pages error:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch all trade history');
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate summary statistics
  const summary = React.useMemo(() => {
    const buyTrades = tradeHistory.filter(t => t.transactionType === 'BUY');
    const sellTrades = tradeHistory.filter(t => t.transactionType === 'SELL');
    
    const totalBuyValue = buyTrades.reduce((sum, t) => sum + (t.tradedQuantity * t.tradedPrice), 0);
    const totalSellValue = sellTrades.reduce((sum, t) => sum + (t.tradedQuantity * t.tradedPrice), 0);
    const totalBrokerage = tradeHistory.reduce((sum, t) => sum + t.brokerageCharges, 0);
    const totalTaxes = tradeHistory.reduce((sum, t) => 
      sum + t.stt + t.sebiTax + t.serviceTax + t.exchangeTransactionCharges + t.stampDuty, 0
    );

    return {
      totalTrades: tradeHistory.length,
      buyTrades: buyTrades.length,
      sellTrades: sellTrades.length,
      totalBuyValue,
      totalSellValue,
      totalBrokerage,
      totalTaxes,
      netAmount: totalSellValue - totalBuyValue - totalBrokerage - totalTaxes
    };
  }, [tradeHistory]);

  const getTransactionColor = (type: string) => {
    return type === 'BUY' ? 'success' : 'danger';
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2
    }).format(amount);
  };

  return (
    <Modal 
      isOpen={isOpen} 
      onOpenChange={onOpenChange}
      size="5xl"
      scrollBehavior="inside"
      classNames={{
        base: "max-h-[90vh]",
        body: "p-0",
      }}
    >
      <ModalContent>
        {(onClose) => (
          <>
            <ModalHeader className="flex flex-col gap-1 px-6 py-4 border-b">
              <div className="flex items-center gap-2">
                <Icon icon="lucide:trending-up" className="w-5 h-5 text-primary" />
                <span>Trade History</span>
              </div>
              <p className="text-sm text-default-500 font-normal">
                View your detailed trade history from Dhan
              </p>
            </ModalHeader>
            <ModalBody className="p-6">
              {/* Date Range Selector */}
              <div className="flex items-center gap-4 mb-6 flex-wrap">
                <div className="flex gap-2">
                  <Input
                    type="date"
                    label="From Date"
                    value={fromDate}
                    onChange={(e) => setFromDate(e.target.value)}
                    className="w-40"
                  />
                  <Input
                    type="date"
                    label="To Date"
                    value={toDate}
                    onChange={(e) => setToDate(e.target.value)}
                    className="w-40"
                  />
                </div>

                <Button
                  color="primary"
                  onPress={handleFetchData}
                  isLoading={isLoading}
                  startContent={!isLoading && <Icon icon="lucide:search" className="w-4 h-4" />}
                >
                  {isLoading ? 'Loading...' : 'Fetch Page 1'}
                </Button>

                <Button
                  color="secondary"
                  variant="flat"
                  onPress={fetchAllPages}
                  isLoading={isLoading}
                  startContent={!isLoading && <Icon icon="lucide:download" className="w-4 h-4" />}
                >
                  {isLoading ? 'Loading...' : 'Fetch All Data'}
                </Button>

                {/* Quick date range buttons */}
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="flat"
                    onPress={() => {
                      const today = new Date().toISOString().split('T')[0];
                      setFromDate(today);
                      setToDate(today);
                    }}
                  >
                    Today
                  </Button>
                  <Button
                    size="sm"
                    variant="flat"
                    onPress={() => {
                      const today = new Date();
                      const sevenDaysAgo = new Date(today);
                      sevenDaysAgo.setDate(today.getDate() - 7);
                      setFromDate(sevenDaysAgo.toISOString().split('T')[0]);
                      setToDate(today.toISOString().split('T')[0]);
                    }}
                  >
                    Last 7 Days
                  </Button>
                  <Button
                    size="sm"
                    variant="flat"
                    onPress={() => {
                      const today = new Date();
                      const thirtyDaysAgo = new Date(today);
                      thirtyDaysAgo.setDate(today.getDate() - 30);
                      setFromDate(thirtyDaysAgo.toISOString().split('T')[0]);
                      setToDate(today.toISOString().split('T')[0]);
                    }}
                  >
                    Last 30 Days
                  </Button>
                </div>
              </div>

              {/* Error Display */}
              {error && (
                <Card className="mb-4 border-danger-200 bg-danger-50">
                  <CardBody className="flex flex-row items-center gap-3 py-3">
                    <Icon icon="lucide:alert-circle" className="w-5 h-5 text-danger flex-shrink-0" />
                    <div>
                      <p className="text-sm font-medium text-danger">Failed to Load Data</p>
                      <p className="text-xs text-danger-600">{error}</p>
                    </div>
                  </CardBody>
                </Card>
              )}

              {/* Debug Info */}
              {tradeHistory.length > 0 && (
                <Card className="mb-4 bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800">
                  <CardBody className="py-3">
                    <div className="flex items-center gap-3 text-sm">
                      <Icon icon="lucide:info" className="w-4 h-4 text-blue-600" />
                      <span className="text-blue-700 dark:text-blue-300">
                        Showing trades from <strong>{fromDate}</strong> to <strong>{toDate}</strong>
                      </span>
                      {tradeHistory.length > 0 && (
                        <span className="text-blue-600 dark:text-blue-400">
                          • Date range in data: <strong>{tradeHistory[tradeHistory.length - 1]?.exchangeTime?.split(' ')[0]}</strong> to <strong>{tradeHistory[0]?.exchangeTime?.split(' ')[0]}</strong>
                        </span>
                      )}
                    </div>
                  </CardBody>
                </Card>
              )}

              {/* Summary Cards */}
              {tradeHistory.length > 0 && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                  <Card>
                    <CardBody className="text-center py-3">
                      <p className="text-2xl font-bold text-primary">{summary.totalTrades}</p>
                      <p className="text-sm text-default-500">Total Trades</p>
                    </CardBody>
                  </Card>
                  <Card>
                    <CardBody className="text-center py-3">
                      <p className="text-2xl font-bold text-success">{summary.buyTrades}</p>
                      <p className="text-sm text-default-500">Buy Orders</p>
                    </CardBody>
                  </Card>
                  <Card>
                    <CardBody className="text-center py-3">
                      <p className="text-2xl font-bold text-danger">{summary.sellTrades}</p>
                      <p className="text-sm text-default-500">Sell Orders</p>
                    </CardBody>
                  </Card>
                  <Card>
                    <CardBody className="text-center py-3">
                      <p className={`text-2xl font-bold ${summary.netAmount >= 0 ? 'text-success' : 'text-danger'}`}>
                        {formatCurrency(summary.netAmount)}
                      </p>
                      <p className="text-sm text-default-500">Net P&L</p>
                    </CardBody>
                  </Card>
                </div>
              )}

              {/* Loading State */}
              {isLoading && (
                <div className="flex justify-center items-center py-8">
                  <Spinner size="lg" />
                </div>
              )}

              {/* Trade History Table */}
              {!isLoading && tradeHistory.length > 0 && (
                <>
                  <Table aria-label="Trade history table" className="mb-4">
                    <TableHeader>
                      <TableColumn>SYMBOL</TableColumn>
                      <TableColumn>TYPE</TableColumn>
                      <TableColumn>QUANTITY</TableColumn>
                      <TableColumn>PRICE</TableColumn>
                      <TableColumn>VALUE</TableColumn>
                      <TableColumn>CHARGES</TableColumn>
                      <TableColumn>TIME</TableColumn>
                    </TableHeader>
                    <TableBody>
                      {tradeHistory.map((trade, index) => (
                        <TableRow key={`${trade.orderId}-${index}`}>
                          <TableCell>
                            <div>
                              <p className="font-medium">{trade.customSymbol || trade.tradingSymbol}</p>
                              <p className="text-xs text-default-500">{trade.securityId}</p>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Chip
                              color={getTransactionColor(trade.transactionType)}
                              variant="flat"
                              size="sm"
                            >
                              {trade.transactionType}
                            </Chip>
                          </TableCell>
                          <TableCell>{trade.tradedQuantity.toLocaleString()}</TableCell>
                          <TableCell>{formatCurrency(trade.tradedPrice)}</TableCell>
                          <TableCell>{formatCurrency(trade.tradedQuantity * trade.tradedPrice)}</TableCell>
                          <TableCell>
                            {formatCurrency(
                              trade.brokerageCharges + trade.stt + trade.sebiTax + 
                              trade.serviceTax + trade.exchangeTransactionCharges + trade.stampDuty
                            )}
                          </TableCell>
                          <TableCell>
                            <div>
                              <p className="text-sm">{trade.exchangeTime}</p>
                              <p className="text-xs text-default-500">{trade.exchangeSegment}</p>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>

                  {/* Pagination */}
                  <div className="flex justify-center">
                    <Pagination
                      total={totalPages}
                      page={currentPage + 1}
                      onChange={(page) => handlePageChange(page - 1)}
                      showControls
                    />
                  </div>
                </>
              )}

              {/* No Data State */}
              {!isLoading && !error && tradeHistory.length === 0 && (
                <div className="text-center py-8">
                  <Icon icon="lucide:inbox" className="w-12 h-12 text-default-300 mx-auto mb-4" />
                  <p className="text-default-500">No trade history found for the selected date range</p>
                  <p className="text-sm text-default-400 mt-2">Try selecting a different date range or check if you have any trades</p>
                </div>
              )}
            </ModalBody>
          </>
        )}
      </ModalContent>
    </Modal>
  );
};
