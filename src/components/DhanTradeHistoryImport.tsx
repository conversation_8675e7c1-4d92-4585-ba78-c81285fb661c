import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dalBody,
  <PERSON>dal<PERSON>ooter,
  <PERSON><PERSON>,
  Card,
  CardBody,
  Progress,
  Chip,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Checkbox,
  Divider,
  Input
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { DhanApiService, DhanTradeHistory } from '../services/dhanApiService';
import { Trade } from '../types/trade';



interface DhanTradeHistoryImportProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  onImport: (trades: Partial<Trade>[]) => void;
  portfolioSize: number;
}

export const DhanTradeHistoryImport: React.FC<DhanTradeHistoryImportProps> = ({
  isOpen,
  onOpenChange,
  onImport,
  portfolioSize
}) => {
  const [isLoading, setIsLoading] = React.useState(false);
  const [dhanTrades, setDhanTrades] = React.useState<DhanTradeHistory[]>([]);
  const [convertedTrades, setConvertedTrades] = React.useState<Partial<Trade>[]>([]);
  const [selectedTrades, setSelectedTrades] = React.useState<Set<number>>(new Set());
  const [error, setError] = React.useState<string | null>(null);
  const [step, setStep] = React.useState<'setup' | 'review' | 'importing'>('setup');


  // Date range state (default to last 30 days)
  const today = new Date();
  const thirtyDaysAgo = new Date(today);
  thirtyDaysAgo.setDate(today.getDate() - 30);

  const [fromDate, setFromDate] = React.useState(thirtyDaysAgo.toISOString().split('T')[0]);
  const [toDate, setToDate] = React.useState(today.toISOString().split('T')[0]);

  const fetchTradeHistory = async () => {
    if (!fromDate || !toDate) {
      setError('Please select both from and to dates');
      return;
    }

    setIsLoading(true);
    setError(null);
    
    try {
      console.log('🔍 Fetching ALL trade history pages for import:', { fromDate, toDate });

      // Fetch all pages of trade history with improved logic
      let allTrades: DhanTradeHistory[] = [];
      let currentPage = 0;
      let hasMoreData = true;
      let consecutiveEmptyPages = 0;
      const maxEmptyPages = 3; // Stop after 3 consecutive empty pages
      const maxPages = 100; // Safety limit to prevent infinite loops

      while (hasMoreData && currentPage < maxPages) {
        console.log(`📄 Fetching page ${currentPage} for import...`);

        try {
          const trades = await DhanApiService.getTradeHistory(fromDate, toDate, currentPage);
          const tradesArray = Array.isArray(trades) ? trades : (trades ? [trades] : []);

          console.log(`📊 Import Page ${currentPage}: Received ${tradesArray.length} trades`);

          if (tradesArray.length === 0) {
            consecutiveEmptyPages++;
            console.log(`⚠️ Empty page ${currentPage} (${consecutiveEmptyPages}/${maxEmptyPages} consecutive empty pages)`);

            if (consecutiveEmptyPages >= maxEmptyPages) {
              console.log('🛑 Import: Stopping due to consecutive empty pages');
              hasMoreData = false;
            }
          } else {
            consecutiveEmptyPages = 0; // Reset counter when we get data
            allTrades = [...allTrades, ...tradesArray];

            // Log some sample data for debugging
            if (tradesArray.length > 0) {
              const firstTrade = tradesArray[0];
              console.log(`📈 Sample import trade from page ${currentPage}:`, {
                tradingSymbol: firstTrade.tradingSymbol,
                customSymbol: firstTrade.customSymbol,
                selectedSymbol: firstTrade.tradingSymbol || firstTrade.customSymbol,
                date: firstTrade.exchangeTime,
                type: firstTrade.transactionType,
                price: firstTrade.tradedPrice
              });
            }
          }

          currentPage++;

          // Add a small delay to avoid overwhelming the API
          if (currentPage % 5 === 0) {
            console.log(`⏸️ Import: Brief pause after ${currentPage} pages...`);
            await new Promise(resolve => setTimeout(resolve, 100));
          }

        } catch (pageError) {
          console.error(`❌ Error fetching import page ${currentPage}:`, pageError);
          consecutiveEmptyPages++;

          if (consecutiveEmptyPages >= maxEmptyPages) {
            console.log('🛑 Import: Stopping due to consecutive errors');
            hasMoreData = false;
          } else {
            currentPage++;
          }
        }
      }

      if (currentPage >= maxPages) {
        console.log(`⚠️ Import: Reached maximum page limit (${maxPages})`);
      }

      console.log(`✅ Import: Fetched ${allTrades.length} trades across ${currentPage} pages`);
      setDhanTrades(allTrades);

      if (allTrades.length === 0) {
        setError(`No trades found for the date range ${fromDate} to ${toDate}. The API might not have data for this period, or all trades might be on pages that couldn't be accessed.`);
        return;
      }

      // Convert to journal format
      const converted = convertTradeHistoryToJournalTrades(allTrades, portfolioSize);
      setConvertedTrades(converted);

      if (converted.length === 0) {
        setError(`Found ${allTrades.length} raw trades but none could be converted to journal format. This might be due to missing buy orders or data format issues.`);
        return;
      }

      // Select all trades by default
      setSelectedTrades(new Set(converted.map((_, index) => index)));

      setStep('review');
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to fetch trade history');
    } finally {
      setIsLoading(false);
    }
  };

  // Convert trade history to journal format
  const convertTradeHistoryToJournalTrades = (
    tradeHistory: DhanTradeHistory[],
    portfolioSize: number
  ): Partial<Trade>[] => {
    // Group trades by trading symbol and date
    const tradeGroups = new Map<string, DhanTradeHistory[]>();
    
    tradeHistory.forEach(trade => {
      const tradeDate = trade.exchangeTime.split(' ')[0];

      // Use customSymbol (company name) directly from Dhan API response
      const symbol = trade.customSymbol || trade.tradingSymbol || 'UNKNOWN';

      // Debug logging to see what symbols we're getting
      console.log(`🔍 Trade symbol debug:`, {
        tradingSymbol: trade.tradingSymbol,
        customSymbol: trade.customSymbol,
        selectedSymbol: symbol,
        securityId: trade.securityId,
        date: tradeDate
      });

      const key = `${symbol}_${tradeDate}`;
      if (!tradeGroups.has(key)) {
        tradeGroups.set(key, []);
      }
      tradeGroups.get(key)!.push(trade);
    });

    const journalTrades: Partial<Trade>[] = [];

    tradeGroups.forEach((trades, key) => {
      const [symbol, date] = key.split('_');
      
      // Separate buy and sell trades
      const buyTrades = trades.filter(t => t.transactionType === 'BUY');
      const sellTrades = trades.filter(t => t.transactionType === 'SELL');

      if (buyTrades.length > 0) {
        // Calculate average entry price and total quantity
        const totalBuyQty = buyTrades.reduce((sum, t) => sum + t.tradedQuantity, 0);
        const totalBuyValue = buyTrades.reduce((sum, t) => sum + (t.tradedQuantity * t.tradedPrice), 0);
        const avgEntry = totalBuyValue / totalBuyQty;

        // Calculate position size
        const positionSize = totalBuyValue;
        const allocation = portfolioSize > 0 ? (positionSize / portfolioSize) * 100 : 0;

        const journalTrade: Partial<Trade> = {
          name: symbol,
          date: date,
          buySell: 'Buy',
          entry: buyTrades[0].tradedPrice,
          avgEntry: avgEntry,
          initialQty: totalBuyQty,
          positionSize: positionSize,
          allocation: allocation,
          positionStatus: sellTrades.length > 0 ? 'Closed' : 'Open',
          // Add trade history metadata
          _dhanMetadata: {
            orderIds: buyTrades.map(t => t.orderId),
            exchangeOrderIds: buyTrades.map(t => t.exchangeOrderId),
            productType: buyTrades[0].productType,
            exchangeSegment: buyTrades[0].exchangeSegment,
            source: 'trade-history' // Mark as imported from trade history
          }
        };

        // If there are sell trades, calculate exit details
        if (sellTrades.length > 0) {
          const totalSellQty = sellTrades.reduce((sum, t) => sum + t.tradedQuantity, 0);
          const totalSellValue = sellTrades.reduce((sum, t) => sum + (t.tradedQuantity * t.tradedPrice), 0);
          const avgExit = totalSellValue / totalSellQty;

          journalTrade.exit1Price = avgExit;
          journalTrade.exit1Qty = totalSellQty;
          journalTrade.exit1Date = sellTrades[0].exchangeTime.split(' ')[0];
          journalTrade.avgExitPrice = avgExit;
          journalTrade.exitedQty = totalSellQty;
          journalTrade.openQty = totalBuyQty - totalSellQty;
          
          if (journalTrade.openQty === 0) {
            journalTrade.positionStatus = 'Closed';
          } else {
            journalTrade.positionStatus = 'Partial';
          }

          // Calculate P&L
          const realizedPL = (avgExit - avgEntry) * totalSellQty;
          journalTrade.realisedAmount = realizedPL;
          journalTrade.plRs = realizedPL;
          journalTrade.pfImpact = portfolioSize > 0 ? (realizedPL / portfolioSize) * 100 : 0;
        }

        journalTrades.push(journalTrade);
      }
    });

    return journalTrades;
  };

  const handleImport = async () => {
    setStep('importing');
    
    try {
      const tradesToImport = convertedTrades.filter((_, index) => selectedTrades.has(index));
      await onImport(tradesToImport);
      onOpenChange(false);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to import trades');
      setStep('review');
    }
  };

  const handleClose = () => {
    setStep('setup');
    setDhanTrades([]);
    setConvertedTrades([]);
    setSelectedTrades(new Set());
    setError(null);
    onOpenChange(false);
  };

  const toggleTradeSelection = (index: number) => {
    const newSelection = new Set(selectedTrades);
    if (newSelection.has(index)) {
      newSelection.delete(index);
    } else {
      newSelection.add(index);
    }
    setSelectedTrades(newSelection);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2
    }).format(amount);
  };

  return (
    <Modal 
      isOpen={isOpen} 
      onOpenChange={onOpenChange}
      size="5xl"
      scrollBehavior="inside"
      isDismissable={step !== 'importing'}
      hideCloseButton={step === 'importing'}
      classNames={{
        base: "max-h-[90vh]",
        body: "py-6",
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <Icon icon="lucide:history" className="w-5 h-5 text-primary" />
            <span>Import Trade History</span>
          </div>
          <p className="text-sm text-foreground-500 font-normal">
            Import historical trades from Dhan API into your trade journal
          </p>
        </ModalHeader>

        <ModalBody>
          {step === 'setup' && (
            <div className="space-y-6">
              {/* Date Range Selection */}
              <Card>
                <CardBody>
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Select Date Range</h3>
                    <div className="flex gap-4 items-end">
                      <Input
                        type="date"
                        label="From Date"
                        value={fromDate}
                        onChange={(e) => setFromDate(e.target.value)}
                        className="flex-1"
                      />
                      <Input
                        type="date"
                        label="To Date"
                        value={toDate}
                        onChange={(e) => setToDate(e.target.value)}
                        className="flex-1"
                      />
                    </div>
                    
                    {/* Quick date range buttons */}
                    <div className="flex gap-2 flex-wrap">
                      <Button
                        size="sm"
                        variant="flat"
                        onPress={() => {
                          const today = new Date().toISOString().split('T')[0];
                          setFromDate(today);
                          setToDate(today);
                        }}
                      >
                        Today
                      </Button>
                      <Button
                        size="sm"
                        variant="flat"
                        onPress={() => {
                          const today = new Date();
                          const sevenDaysAgo = new Date(today);
                          sevenDaysAgo.setDate(today.getDate() - 7);
                          setFromDate(sevenDaysAgo.toISOString().split('T')[0]);
                          setToDate(today.toISOString().split('T')[0]);
                        }}
                      >
                        Last 7 Days
                      </Button>
                      <Button
                        size="sm"
                        variant="flat"
                        onPress={() => {
                          const today = new Date();
                          const thirtyDaysAgo = new Date(today);
                          thirtyDaysAgo.setDate(today.getDate() - 30);
                          setFromDate(thirtyDaysAgo.toISOString().split('T')[0]);
                          setToDate(today.toISOString().split('T')[0]);
                        }}
                      >
                        Last 30 Days
                      </Button>
                    </div>
                  </div>
                </CardBody>
              </Card>

              {/* Info Card */}
              <Card className="bg-primary-50 dark:bg-primary-950 border border-primary-200 dark:border-primary-800">
                <CardBody>
                  <div className="flex items-center gap-3">
                    <Icon icon="lucide:info" className="w-5 h-5 text-primary-600" />
                    <div>
                      <p className="font-medium text-primary-700 dark:text-primary-300">
                        Import Historical Trades
                      </p>
                      <p className="text-sm text-primary-600 dark:text-primary-400">
                        This will fetch trade history from the selected date range and convert them to your journal format
                      </p>
                    </div>
                  </div>
                </CardBody>
              </Card>

              {error && (
                <Card className="bg-danger-50 border border-danger-200 dark:bg-danger-950 dark:border-danger-800">
                  <CardBody>
                    <div className="flex items-center gap-3">
                      <Icon icon="lucide:alert-circle" className="w-5 h-5 text-danger-600" />
                      <div>
                        <p className="font-medium text-danger-700 dark:text-danger-300">
                          Import Failed
                        </p>
                        <p className="text-sm text-danger-600 dark:text-danger-400">
                          {error}
                        </p>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              )}

              <Button
                color="primary"
                size="lg"
                onPress={fetchTradeHistory}
                isLoading={isLoading}
                startContent={!isLoading && <Icon icon="lucide:history" className="w-5 h-5" />}
                className="w-full"
              >
                {isLoading ? 'Fetching Trade History...' : 'Fetch Trade History'}
              </Button>
            </div>
          )}

          {step === 'review' && (
            <div className="space-y-6">
              {/* Debug Info */}
              <Card className="mb-4 bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800">
                <CardBody className="py-3">
                  <div className="flex items-center gap-3 text-sm">
                    <Icon icon="lucide:info" className="w-4 h-4 text-blue-600" />
                    <span className="text-blue-700 dark:text-blue-300">
                      Fetched from <strong>{fromDate}</strong> to <strong>{toDate}</strong>
                    </span>
                    {dhanTrades.length > 0 && (
                      <span className="text-blue-600 dark:text-blue-400">
                        • Raw trades: <strong>{dhanTrades.length}</strong> • Converted: <strong>{convertedTrades.length}</strong>
                      </span>
                    )}
                    {dhanTrades.length > 0 && (
                      <span className="text-blue-600 dark:text-blue-400">
                        • Date range: <strong>{dhanTrades[dhanTrades.length - 1]?.exchangeTime?.split(' ')[0]}</strong> to <strong>{dhanTrades[0]?.exchangeTime?.split(' ')[0]}</strong>
                      </span>
                    )}
                  </div>
                </CardBody>
              </Card>

              {/* Summary */}
              <Card className="bg-success-50 dark:bg-success-950 border border-success-200 dark:border-success-800">
                <CardBody>
                  <div className="flex items-center gap-3">
                    <Icon icon="lucide:check-circle" className="w-5 h-5 text-success-600" />
                    <div>
                      <p className="font-medium text-success-700 dark:text-success-300">
                        Found {convertedTrades.length} trades to import
                      </p>
                      <p className="text-sm text-success-600 dark:text-success-400">
                        Review and select the trades you want to import into your journal
                      </p>
                    </div>
                  </div>
                </CardBody>
              </Card>

              {/* Selection Controls */}
              <div className="flex justify-between items-center">
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="flat"
                    onPress={() => setSelectedTrades(new Set(convertedTrades.map((_, i) => i)))}
                  >
                    Select All
                  </Button>
                  <Button
                    size="sm"
                    variant="flat"
                    onPress={() => setSelectedTrades(new Set())}
                  >
                    Select None
                  </Button>
                </div>
                <p className="text-sm text-foreground-500">
                  {selectedTrades.size} of {convertedTrades.length} selected
                </p>
              </div>

              {/* Trades Table */}
              {convertedTrades.length > 0 && (
                <Card>
                  <CardBody className="p-0">
                    <Table removeWrapper>
                      <TableHeader>
                        <TableColumn>SELECT</TableColumn>
                        <TableColumn>SYMBOL</TableColumn>
                        <TableColumn>DATE</TableColumn>
                        <TableColumn>TYPE</TableColumn>
                        <TableColumn>ENTRY</TableColumn>
                        <TableColumn>QUANTITY</TableColumn>
                        <TableColumn>POSITION SIZE</TableColumn>
                        <TableColumn>STATUS</TableColumn>
                      </TableHeader>
                      <TableBody>
                        {convertedTrades.map((trade, index) => (
                          <TableRow key={index}>
                            <TableCell>
                              <Checkbox
                                isSelected={selectedTrades.has(index)}
                                onValueChange={() => toggleTradeSelection(index)}
                              />
                            </TableCell>
                            <TableCell className="font-medium">{trade.name}</TableCell>
                            <TableCell>{trade.date}</TableCell>
                            <TableCell>
                              <Chip
                                size="sm"
                                color={trade.buySell === 'Buy' ? 'success' : 'danger'}
                                variant="flat"
                              >
                                {trade.buySell}
                              </Chip>
                            </TableCell>
                            <TableCell>{formatCurrency(trade.entry || 0)}</TableCell>
                            <TableCell>{trade.initialQty?.toLocaleString()}</TableCell>
                            <TableCell>{formatCurrency(trade.positionSize || 0)}</TableCell>
                            <TableCell>
                              <Chip
                                size="sm"
                                color={
                                  trade.positionStatus === 'Closed' ? 'success' :
                                  trade.positionStatus === 'Partial' ? 'warning' : 'primary'
                                }
                                variant="flat"
                              >
                                {trade.positionStatus}
                              </Chip>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </CardBody>
                </Card>
              )}
            </div>
          )}

          {step === 'importing' && (
            <div className="space-y-6">
              <Card>
                <CardBody className="text-center py-8">
                  <Progress
                    size="lg"
                    isIndeterminate
                    color="primary"
                    className="mb-4"
                  />
                  <p className="text-lg font-medium">Importing trades...</p>
                  <p className="text-sm text-foreground-500 mt-2">
                    Please wait while we import {selectedTrades.size} trades into your journal
                  </p>
                </CardBody>
              </Card>
            </div>
          )}
        </ModalBody>

        <ModalFooter>
          {step === 'setup' && (
            <Button variant="light" onPress={handleClose}>
              Cancel
            </Button>
          )}

          {step === 'review' && (
            <>
              <Button variant="light" onPress={() => setStep('setup')}>
                Back
              </Button>
              <Button
                color="primary"
                onPress={handleImport}
                isDisabled={selectedTrades.size === 0}
                startContent={<Icon icon="lucide:download" className="w-4 h-4" />}
              >
                Import {selectedTrades.size} Trades
              </Button>
            </>
          )}

          {step === 'importing' && (
            <Button variant="light" isDisabled>
              Importing...
            </Button>
          )}
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
