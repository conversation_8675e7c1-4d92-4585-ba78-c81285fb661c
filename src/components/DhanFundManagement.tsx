import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  ModalHeader,
  <PERSON>dalBody,
  <PERSON>dal<PERSON>ooter,
  <PERSON>ton,
  Card,
  CardBody,
  CardHeader,
  Divider,
  Spinner,
  Input,
  Select,
  SelectItem,
  Chip,
  Progress,
  Autocomplete,
  AutocompleteItem
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { DhanApiService, DhanFundLimit, DhanMarginCalculatorRequest, DhanMarginCalculatorResponse } from '../services/dhanApiService';
import InstrumentService, { ParsedInstrument } from '../services/instrumentService';

interface DhanFundManagementProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
}

export function DhanFundManagement({ isOpen, onOpenChange }: DhanFundManagementProps) {
  const [fundLimit, setFundLimit] = useState<DhanFundLimit | null>(null);
  const [marginCalculation, setMarginCalculation] = useState<DhanMarginCalculatorResponse | null>(null);
  const [isLoadingFunds, setIsLoadingFunds] = useState(false);
  const [isCalculatingMargin, setIsCalculatingMargin] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Instrument search state
  const [instrumentSearch, setInstrumentSearch] = useState('');
  const [searchResults, setSearchResults] = useState<ParsedInstrument[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [selectedInstrument, setSelectedInstrument] = useState<ParsedInstrument | null>(null);
  const [popularInstruments, setPopularInstruments] = useState<ParsedInstrument[]>([]);

  // Margin calculator form state
  const [marginForm, setMarginForm] = useState<Partial<DhanMarginCalculatorRequest>>({
    exchangeSegment: 'NSE_EQ',
    transactionType: 'BUY',
    productType: 'CNC',
    quantity: 1,
    price: 0,
    securityId: '',
    triggerPrice: 0
  });

  const fetchFundLimit = async () => {
    setIsLoadingFunds(true);
    setError(null);
    try {
      const data = await DhanApiService.getFundLimit();
      setFundLimit(data);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to fetch fund limit');
    } finally {
      setIsLoadingFunds(false);
    }
  };

  const calculateMargin = async () => {
    if (!marginForm.securityId || !marginForm.price || !marginForm.quantity) {
      setError('Please fill all required fields for margin calculation');
      return;
    }

    setIsCalculatingMargin(true);
    setError(null);
    try {
      // Get profile to get dhanClientId
      const profile = await DhanApiService.getProfile();
      
      const request: DhanMarginCalculatorRequest = {
        dhanClientId: profile.dhanClientId,
        exchangeSegment: marginForm.exchangeSegment as any,
        transactionType: marginForm.transactionType as any,
        quantity: marginForm.quantity!,
        productType: marginForm.productType as any,
        securityId: marginForm.securityId!,
        price: marginForm.price!,
        triggerPrice: marginForm.triggerPrice
      };

      const data = await DhanApiService.calculateMargin(request);
      setMarginCalculation(data);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to calculate margin');
    } finally {
      setIsCalculatingMargin(false);
    }
  };

  // Search instruments
  const searchInstruments = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      const results = await InstrumentService.searchInstruments(
        query,
        marginForm.exchangeSegment?.split('_')[0], // Extract exchange from segment
        marginForm.exchangeSegment?.split('_')[1], // Extract segment
        10
      );
      setSearchResults(results);
    } catch (error) {
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Load popular instruments
  const loadPopularInstruments = async () => {
    try {
      const popular = await InstrumentService.getPopularInstruments();
      setPopularInstruments(popular);
    } catch (error) {
      // Ignore errors for popular instruments
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchFundLimit();
      loadPopularInstruments();
    }
  }, [isOpen]);

  // Search instruments when search term changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      searchInstruments(instrumentSearch);
    }, 300); // Debounce search

    return () => clearTimeout(timeoutId);
  }, [instrumentSearch, marginForm.exchangeSegment]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const utilizationPercentage = fundLimit 
    ? (fundLimit.utilizedAmount / fundLimit.sodLimit) * 100 
    : 0;

  return (
    <Modal 
      isOpen={isOpen} 
      onOpenChange={onOpenChange}
      size="4xl"
      scrollBehavior="inside"
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <Icon icon="lucide:wallet" className="w-5 h-5" />
            Fund Management
          </div>
          <p className="text-sm text-default-500">
            View account balance and calculate margin requirements
          </p>
        </ModalHeader>
        
        <ModalBody className="gap-6">
          {error && (
            <Card className="border-danger/50 bg-danger/5">
              <CardBody className="p-3">
                <div className="flex items-center gap-2">
                  <Icon icon="lucide:alert-circle" className="text-danger w-4 h-4" />
                  <span className="text-danger text-sm">{error}</span>
                </div>
              </CardBody>
            </Card>
          )}

          {/* Fund Limit Section */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between w-full">
                <h3 className="text-lg font-semibold">Account Balance</h3>
                <Button
                  size="sm"
                  variant="flat"
                  onPress={fetchFundLimit}
                  isLoading={isLoadingFunds}
                  startContent={!isLoadingFunds && <Icon icon="lucide:refresh-cw" className="w-4 h-4" />}
                >
                  Refresh
                </Button>
              </div>
            </CardHeader>
            <CardBody className="pt-0">
              {isLoadingFunds ? (
                <div className="flex justify-center py-8">
                  <Spinner size="lg" />
                </div>
              ) : fundLimit ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <p className="text-sm text-default-500">Available Balance</p>
                    <p className="text-xl font-bold text-success">
                      {formatCurrency(fundLimit.availabelBalance)}
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <p className="text-sm text-default-500">Withdrawable Balance</p>
                    <p className="text-lg font-semibold">
                      {formatCurrency(fundLimit.withdrawableBalance)}
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <p className="text-sm text-default-500">Start of Day Limit</p>
                    <p className="text-lg font-semibold">
                      {formatCurrency(fundLimit.sodLimit)}
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <p className="text-sm text-default-500">Utilized Amount</p>
                    <p className="text-lg font-semibold text-warning">
                      {formatCurrency(fundLimit.utilizedAmount)}
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <p className="text-sm text-default-500">Collateral Amount</p>
                    <p className="text-lg font-semibold">
                      {formatCurrency(fundLimit.collateralAmount)}
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <p className="text-sm text-default-500">Receivable Amount</p>
                    <p className="text-lg font-semibold">
                      {formatCurrency(fundLimit.receiveableAmount)}
                    </p>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-default-500">
                  Click refresh to load fund information
                </div>
              )}
              
              {fundLimit && (
                <>
                  <Divider className="my-4" />
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-default-500">Fund Utilization</span>
                      <span className="text-sm font-medium">{utilizationPercentage.toFixed(1)}%</span>
                    </div>
                    <Progress
                      value={utilizationPercentage}
                      color={utilizationPercentage > 80 ? "danger" : utilizationPercentage > 60 ? "warning" : "success"}
                      className="w-full"
                      aria-label={`Fund utilization: ${utilizationPercentage.toFixed(1)}%`}
                    />
                  </div>
                </>
              )}
            </CardBody>
          </Card>

          {/* Margin Calculator Section */}
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">Margin Calculator</h3>
            </CardHeader>
            <CardBody className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Select
                  label="Exchange Segment"
                  selectedKeys={marginForm.exchangeSegment ? [marginForm.exchangeSegment] : []}
                  onSelectionChange={(keys) => {
                    const value = Array.from(keys)[0] as string;
                    setMarginForm(prev => ({ ...prev, exchangeSegment: value as any }));
                  }}
                >
                  <SelectItem key="NSE_EQ">NSE Equity</SelectItem>
                  <SelectItem key="NSE_FNO">NSE F&O</SelectItem>
                  <SelectItem key="BSE_EQ">BSE Equity</SelectItem>
                  <SelectItem key="BSE_FNO">BSE F&O</SelectItem>
                  <SelectItem key="MCX_COMM">MCX Commodity</SelectItem>
                </Select>

                <Select
                  label="Transaction Type"
                  selectedKeys={marginForm.transactionType ? [marginForm.transactionType] : []}
                  onSelectionChange={(keys) => {
                    const value = Array.from(keys)[0] as string;
                    setMarginForm(prev => ({ ...prev, transactionType: value as any }));
                  }}
                >
                  <SelectItem key="BUY">Buy</SelectItem>
                  <SelectItem key="SELL">Sell</SelectItem>
                </Select>

                <Select
                  label="Product Type"
                  selectedKeys={marginForm.productType ? [marginForm.productType] : []}
                  onSelectionChange={(keys) => {
                    const value = Array.from(keys)[0] as string;
                    setMarginForm(prev => ({ ...prev, productType: value as any }));
                  }}
                >
                  <SelectItem key="CNC">CNC (Delivery)</SelectItem>
                  <SelectItem key="INTRADAY">Intraday</SelectItem>
                  <SelectItem key="MARGIN">Margin</SelectItem>
                  <SelectItem key="MTF">MTF</SelectItem>
                  <SelectItem key="CO">Cover Order</SelectItem>
                  <SelectItem key="BO">Bracket Order</SelectItem>
                </Select>

                <div className="space-y-2">
                  <Autocomplete
                    label="Search Instrument"
                    placeholder="Type symbol name (e.g., RELIANCE, TCS)"
                    value={instrumentSearch}
                    onValueChange={setInstrumentSearch}
                    onSelectionChange={(key) => {
                      if (key) {
                        const instrument = searchResults.find(inst => inst.securityId === key) ||
                                         popularInstruments.find(inst => inst.securityId === key);
                        if (instrument) {
                          setSelectedInstrument(instrument);
                          setMarginForm(prev => ({
                            ...prev,
                            securityId: instrument.securityId,
                            exchangeSegment: `${instrument.exchange}_${instrument.segment}` as any
                          }));
                          setInstrumentSearch(instrument.symbol);
                        }
                      }
                    }}
                    startContent={<Icon icon="lucide:search" className="w-4 h-4 text-default-400" />}
                    endContent={isSearching && <Spinner size="sm" />}
                  >
                    {/* Show search results first */}
                    {searchResults.map((instrument) => (
                      <AutocompleteItem
                        key={instrument.securityId}
                        value={instrument.securityId}
                        textValue={instrument.symbol}
                      >
                        <div className="flex justify-between items-center">
                          <div>
                            <div className="font-medium">{instrument.symbol}</div>
                            <div className="text-xs text-default-500">{instrument.displayName}</div>
                          </div>
                          <div className="text-xs text-default-400">
                            {instrument.exchange} • ID: {instrument.securityId}
                          </div>
                        </div>
                      </AutocompleteItem>
                    ))}

                    {/* Show popular instruments if no search */}
                    {!instrumentSearch && popularInstruments.map((instrument) => (
                      <AutocompleteItem
                        key={instrument.securityId}
                        value={instrument.securityId}
                        textValue={instrument.symbol}
                      >
                        <div className="flex justify-between items-center">
                          <div>
                            <div className="font-medium">{instrument.symbol}</div>
                            <div className="text-xs text-default-500">{instrument.displayName}</div>
                          </div>
                          <div className="text-xs text-default-400">
                            {instrument.exchange} • ID: {instrument.securityId}
                          </div>
                        </div>
                      </AutocompleteItem>
                    ))}
                  </Autocomplete>

                  {selectedInstrument && (
                    <Card className="bg-primary/5 border-primary/20">
                      <CardBody className="p-3">
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium text-primary">{selectedInstrument.symbol}</div>
                            <div className="text-xs text-default-600">{selectedInstrument.displayName}</div>
                            <div className="text-xs text-default-500">
                              {selectedInstrument.exchange} • {selectedInstrument.segment} •
                              Lot: {selectedInstrument.lotSize} • Tick: ₹{selectedInstrument.tickSize}
                            </div>
                          </div>
                          <Chip size="sm" color="primary" variant="flat">
                            ID: {selectedInstrument.securityId}
                          </Chip>
                        </div>
                      </CardBody>
                    </Card>
                  )}
                </div>

                <Input
                  label="Quantity"
                  type="number"
                  value={marginForm.quantity?.toString() || ''}
                  onValueChange={(value) => setMarginForm(prev => ({ ...prev, quantity: parseInt(value) || 0 }))}
                />

                <Input
                  label="Price"
                  type="number"
                  step="0.01"
                  value={marginForm.price?.toString() || ''}
                  onValueChange={(value) => setMarginForm(prev => ({ ...prev, price: parseFloat(value) || 0 }))}
                />
              </div>

              <Button
                color="primary"
                onPress={calculateMargin}
                isLoading={isCalculatingMargin}
                startContent={!isCalculatingMargin && <Icon icon="lucide:calculator" className="w-4 h-4" />}
                className="w-full md:w-auto"
              >
                Calculate Margin
              </Button>

              {marginCalculation && (
                <Card className="bg-default-50">
                  <CardBody>
                    <h4 className="font-semibold mb-3">Margin Calculation Result</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <p className="text-sm text-default-500">Total Margin Required</p>
                        <p className="text-lg font-bold text-primary">
                          {formatCurrency(marginCalculation.totalMargin)}
                        </p>
                      </div>
                      
                      <div className="space-y-2">
                        <p className="text-sm text-default-500">Available Balance</p>
                        <p className="text-lg font-semibold">
                          {formatCurrency(marginCalculation.availableBalance)}
                        </p>
                      </div>
                      
                      <div className="space-y-2">
                        <p className="text-sm text-default-500">SPAN Margin</p>
                        <p className="text-lg font-semibold">
                          {formatCurrency(marginCalculation.spanMargin)}
                        </p>
                      </div>
                      
                      <div className="space-y-2">
                        <p className="text-sm text-default-500">Exposure Margin</p>
                        <p className="text-lg font-semibold">
                          {formatCurrency(marginCalculation.exposureMargin)}
                        </p>
                      </div>
                      
                      <div className="space-y-2">
                        <p className="text-sm text-default-500">Variable Margin</p>
                        <p className="text-lg font-semibold">
                          {formatCurrency(marginCalculation.variableMargin)}
                        </p>
                      </div>
                      
                      <div className="space-y-2">
                        <p className="text-sm text-default-500">Brokerage</p>
                        <p className="text-lg font-semibold">
                          {formatCurrency(marginCalculation.brokerage)}
                        </p>
                      </div>
                      
                      <div className="space-y-2">
                        <p className="text-sm text-default-500">Leverage</p>
                        <Chip color="secondary" variant="flat">
                          {marginCalculation.leverage}x
                        </Chip>
                      </div>
                      
                      <div className="space-y-2">
                        <p className="text-sm text-default-500">Insufficient Balance</p>
                        <p className={`text-lg font-semibold ${marginCalculation.insufficientBalance > 0 ? 'text-danger' : 'text-success'}`}>
                          {formatCurrency(marginCalculation.insufficientBalance)}
                        </p>
                      </div>
                    </div>
                    
                    {marginCalculation.insufficientBalance > 0 && (
                      <Card className="mt-4 border-danger/50 bg-danger/5">
                        <CardBody className="p-3">
                          <div className="flex items-center gap-2">
                            <Icon icon="lucide:alert-triangle" className="text-danger w-4 h-4" />
                            <span className="text-danger text-sm font-medium">
                              Insufficient funds! You need {formatCurrency(marginCalculation.insufficientBalance)} more.
                            </span>
                          </div>
                        </CardBody>
                      </Card>
                    )}
                  </CardBody>
                </Card>
              )}
            </CardBody>
          </Card>
        </ModalBody>
        
        <ModalFooter>
          <Button variant="light" onPress={() => onOpenChange(false)}>
            Close
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
