@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  font-family: 'Inter', sans-serif;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--heroui-content2));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--heroui-default-300));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--heroui-default-400));
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

/* Table improvements */
.heroui-table-cell {
  padding: 10px 12px !important;
}

/* Input focus styles */
.heroui-input:focus-within {
  box-shadow: 0 0 0 2px hsl(var(--heroui-primary-200)) !important;
}

/* Card hover effect */
.heroui-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.heroui-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}