@echo off
echo 🚀 Starting Dhan API Proxy Server and Trade Journal Application

REM Start proxy server
echo 📡 Starting proxy server on port 3001...
cd proxy-server
call npm install >nul 2>&1
start "Dhan Proxy Server" cmd /k "npm start"

REM Wait for proxy to start
timeout /t 3 /nobreak >nul

REM Check if proxy is running (simplified check)
echo ✅ Proxy server should be running on http://localhost:3001

REM Go back to main directory
cd ..

REM Start main application
echo 🎯 Starting trade journal application...
start "Trade Journal" cmd /k "npm run dev"

echo 🌟 Both services are starting:
echo    📡 Proxy Server: http://localhost:3001
echo    🎯 Trade Journal: Check the opened terminal windows
echo.
echo Press any key to exit...
pause >nul
