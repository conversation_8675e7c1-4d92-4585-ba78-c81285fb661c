const express = require('express');
const fetch = require('node-fetch');

const app = express();
const PORT = process.env.PORT || 3001;

// Ultra-permissive CORS for development
app.use((req, res, next) => {
  // Set CORS headers for all requests
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', '*');
  res.header('Access-Control-Allow-Headers', '*');
  res.header('Access-Control-Max-Age', '86400');

  // Handle preflight
  if (req.method === 'OPTIONS') {
    console.log(`✅ OPTIONS request for: ${req.url}`);
    return res.status(200).end();
  }

  next();
});

app.use(express.json());

// Dhan API base URL
const DHAN_API_BASE = 'https://api.dhan.co/v2';

// Proxy middleware for Dhan API
app.use('/api/dhan', async (req, res) => {
  try {
    const { method, headers, body } = req;
    const path = req.path;
    const queryString = req.url.includes('?') ? req.url.substring(req.url.indexOf('?')) : '';
    const url = `${DHAN_API_BASE}${path}${queryString}`;

    console.log(`🔄 Proxying ${method} request to: ${url}`);
    console.log(`🔑 Access Token: ${headers['access-token'] ? '[Present]' : '[Missing]'}`);
    if (queryString) {
      console.log(`🔍 Query parameters: ${queryString}`);
    }

    // Forward the access token from the request headers
    const proxyHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    // Forward the access-token header if present
    if (headers['access-token']) {
      proxyHeaders['access-token'] = headers['access-token'];
    }

    const fetchOptions = {
      method,
      headers: proxyHeaders,
    };

    // Add body for POST/PUT requests
    if (method === 'POST' || method === 'PUT') {
      fetchOptions.body = JSON.stringify(body);
    }

    const response = await fetch(url, fetchOptions);
    const data = await response.text();

    console.log(`📡 Dhan API responded with status: ${response.status}`);

    // Set response headers (CORS headers are already set by middleware)
    res.status(response.status);
    res.set('Content-Type', response.headers.get('content-type') || 'application/json');

    // Try to parse as JSON, fallback to text
    try {
      const jsonData = JSON.parse(data);
      res.json(jsonData);
    } catch (e) {
      res.send(data);
    }

  } catch (error) {
    console.error('❌ Proxy error:', error);
    res.status(500).json({
      error: 'Proxy server error',
      message: error.message
    });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Dhan API Proxy Server is running',
    timestamp: new Date().toISOString()
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Dhan API Proxy Server running on port ${PORT}`);
  console.log(`📡 Proxying requests to: ${DHAN_API_BASE}`);
  console.log(`🌐 Health check: http://localhost:${PORT}/health`);
});

module.exports = app;
