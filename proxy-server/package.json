{"name": "dhan-api-proxy", "version": "1.0.0", "description": "CORS proxy server for Dhan API integration", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["dhan", "api", "proxy", "cors", "trading"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "node-fetch": "^2.7.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}