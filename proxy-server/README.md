# Dhan API Proxy Server

This is a CORS proxy server that enables the trade journal application to connect to the Dhan API from the browser by bypassing CORS restrictions.

## 🚀 Quick Setup

### 1. Install Dependencies

```bash
cd proxy-server
npm install
```

### 2. Start the Proxy Server

```bash
# Development mode (with auto-restart)
npm run dev

# Production mode
npm start
```

The proxy server will start on `http://localhost:3001`

### 3. Verify Setup

Check if the proxy is running:
```bash
curl http://localhost:3001/health
```

You should see:
```json
{
  "status": "OK",
  "message": "Dhan API Proxy Server is running",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 🔧 How It Works

### Request Flow
```
Trade Journal App → Proxy Server → Dhan API
    (localhost:3000)   (localhost:3001)   (api.dhan.co)
```

### URL Mapping
- **Frontend Request**: `http://localhost:3001/api/dhan/profile`
- **Proxied to**: `https://api.dhan.co/v2/profile`

### Headers Forwarding
The proxy automatically forwards:
- `access-token` header (your Dhan API token)
- `Content-Type` and `Accept` headers
- Request body for POST/PUT requests

## 📝 API Endpoints Available

All Dhan API endpoints are available through the proxy:

- **Profile**: `GET /api/dhan/profile`
- **Orders**: `GET /api/dhan/orders`
- **Trades**: `GET /api/dhan/trades`
- **Holdings**: `GET /api/dhan/holdings`
- **Positions**: `GET /api/dhan/positions`
- **Forever Orders**: `GET /api/dhan/forever/all`

## 🛠️ Configuration

### Environment Variables
- `PORT`: Server port (default: 3001)
- `NODE_ENV`: Environment mode

### CORS Settings
The proxy allows requests from:
- `http://localhost:3000` (React default)
- `http://localhost:5173` (Vite default)
- `http://127.0.0.1:3000`
- `http://127.0.0.1:5173`

## 🔍 Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   Error: listen EADDRINUSE: address already in use :::3001
   ```
   **Solution**: Change the port or kill the existing process
   ```bash
   # Kill process on port 3001
   lsof -ti:3001 | xargs kill -9
   
   # Or use a different port
   PORT=3002 npm start
   ```

2. **Connection Refused**
   ```
   Error: connect ECONNREFUSED 127.0.0.1:3001
   ```
   **Solution**: Make sure the proxy server is running
   ```bash
   npm run dev
   ```

3. **CORS Errors**
   ```
   Access to fetch at 'http://localhost:3001' from origin 'http://localhost:3000' has been blocked by CORS policy
   ```
   **Solution**: Check if your frontend URL is in the CORS whitelist in `server.js`

### Debug Mode
Enable detailed logging by modifying `server.js`:
```javascript
// Add this for more detailed logs
app.use((req, res, next) => {
  console.log(`${req.method} ${req.url}`, req.headers);
  next();
});
```

## 🚀 Production Deployment

### Option 1: Same Domain Deployment
Deploy the proxy on the same domain as your frontend:
- Frontend: `https://yourdomain.com`
- Proxy: `https://yourdomain.com/api/dhan`

### Option 2: Separate Server
Deploy on a separate server and update CORS settings:
```javascript
app.use(cors({
  origin: ['https://yourdomain.com'],
  credentials: true
}));
```

## 📦 Dependencies

- **express**: Web framework for Node.js
- **cors**: Enable CORS with various options
- **node-fetch**: Fetch API for Node.js (for making HTTP requests)
- **nodemon**: Development dependency for auto-restart

## 🔐 Security Notes

- The proxy forwards your Dhan API token, ensure HTTPS in production
- Only whitelist trusted domains in CORS settings
- Consider rate limiting for production use
- Monitor proxy logs for suspicious activity

## 📞 Support

If you encounter issues:
1. Check the proxy server logs
2. Verify your Dhan API token is valid
3. Ensure the proxy server is accessible from your frontend
4. Test the proxy health endpoint first
