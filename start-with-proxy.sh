#!/bin/bash

# Start both the proxy server and the main application

echo "🚀 Starting Dhan API Proxy Server and Trade Journal Application"

# Function to cleanup background processes
cleanup() {
    echo "🛑 Stopping all processes..."
    kill $PROXY_PID $APP_PID 2>/dev/null
    exit 0
}

# Set up trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Start proxy server in background
echo "📡 Starting proxy server on port 3001..."
cd proxy-server
npm install > /dev/null 2>&1
npm start &
PROXY_PID=$!

# Wait a moment for proxy to start
sleep 3

# Check if proxy is running
if curl -s http://localhost:3001/health > /dev/null; then
    echo "✅ Proxy server is running on http://localhost:3001"
else
    echo "❌ Failed to start proxy server"
    exit 1
fi

# Go back to main directory
cd ..

# Start main application
echo "🎯 Starting trade journal application..."
npm run dev &
APP_PID=$!

echo "🌟 Both services are running:"
echo "   📡 Proxy Server: http://localhost:3001"
echo "   🎯 Trade Journal: http://localhost:3000 (or check your terminal)"
echo ""
echo "Press Ctrl+C to stop both services"

# Wait for both processes
wait $PROXY_PID $APP_PID
